# 🤖 Emma AI - Guía de Inicio Rápido

## 🎯 ¿Qué es Emma AI?

**Emma AI** es el agente principal de Emma Studio que **reemplaza agencias completas de marketing**. Está construida sobre **AgenticSeek** (open source) con funcionalidades cloud personalizadas.

## 🚀 Acceso Inmediato

### 📍 URLs Principales:
- **Producción**: `http://localhost:5173/emma-ai`
- **Desarrollo**: `http://localhost:3002/emma-ai`
- **Dashboard**: `http://localhost:3002/dashboard/emma-ai`

### 🔑 Credenciales ya configuradas:
- **Serper API**: `2187e03c0d1710eeaa3e669daf6a4fcddc1b84cb`
- **Browserless**: `2SP6LRG5ebh7ohfc3b60651a5f2f574bc95ee4e3c8caf2a58`
- **Gemini**: Usar clave existente de Emma Studio

## ⚡ Inicio Rápido (2 minutos)

### 1. **Verificar configuración**:
```bash
python scripts/verify_emma_setup.py
```

### 2. **Iniciar servicios**:
```bash
# Terminal 1 - Backend
cd backend
python -m uvicorn app.main:app --reload --port 8001

# Terminal 2 - Frontend  
cd client
npm run dev
```

### 3. **Acceder a Emma**:
Ir a: `http://localhost:3002/emma-ai`

## 🏗️ Arquitectura Simplificada

```
Usuario → Emma AI Interface → Emma Orchestrator → AgenticSeek → Agentes Especializados
```

### 🤖 Agentes Disponibles:
- **CasualAgent**: Conversación general y coordinación
- **BrowserAgent**: Navegación web + screenshots (Browserless.io)
- **CoderAgent**: Programación y desarrollo
- **PlannerAgent**: Planificación de tareas complejas
- **FileAgent**: Gestión de archivos y documentos
- **McpAgent**: Protocolos MCP avanzados

## 📁 Archivos Importantes

### 🎨 Frontend:
- **Interfaz**: `client/src/pages/emma-agenticseek-page.tsx`
- **Rutas**: `client/src/App.tsx` (líneas 181-203)
- **Estilos**: `client/src/pages/emma-agenticseek.css`

### ⚙️ Backend:
- **AgenticSeek**: `backend/app/agenticseek/` (completo)
- **Orchestrator**: `emma-integration/emma_orchestrator.py`
- **API**: `backend/app/agenticseek/api.py`

### 📚 Documentación:
- **Guía rápida**: `client/src/pages/EMMA_AI_README.md`
- **Arquitectura**: `docs/EMMA_AI_ARCHITECTURE.md`
- **Este archivo**: `EMMA_AI_QUICK_START.md`

## 🔧 Configuración Avanzada

### Variables de Entorno (`backend/.env`):
```bash
# Emma Studio (existentes)
GEMINI_API_KEY=tu_clave_gemini
STABILITY_API_KEY=tu_clave_stability

# Emma AI (nuevas)
SERPER_API_KEY=2187e03c0d1710eeaa3e669daf6a4fcddc1b84cb
BROWSERLESS_API_KEY=2SP6LRG5ebh7ohfc3b60651a5f2f574bc95ee4e3c8caf2a58
BROWSERLESS_URL=https://chrome.browserless.io
```

### Puertos:
- **Frontend**: 3002 (desarrollo) / 5173 (producción)
- **Backend**: 8001 (Emma Studio + AgenticSeek integrado)

## 🧪 Testing

### Tests disponibles:
```bash
# Test básico Emma + AgenticSeek
python backend/test_emma_agenticseek.py

# Test cloud browser
python backend/test_cloud_browser.py

# Test API simple
python backend/test_api_simple.py
```

## 🎨 Características Únicas

### ✨ "Wow Factor" Visual:
- Screenshots en tiempo real de navegación web
- Visualización de agentes trabajando en equipo
- Progress indicators de tareas complejas
- Chat unificado (no tabs separados)

### 🌐 Cloud-First:
- **Browserless.io**: Screenshots y navegación
- **Serper**: Búsquedas web avanzadas
- **Gemini**: Análisis de imágenes y LLM
- **Stability AI**: Generación de imágenes

### 🤝 Multi-Agente Real:
- Coordinación automática entre agentes
- Delegación inteligente de tareas
- Planificación colaborativa
- Resultados unificados

## 🚨 Puntos Críticos

### ❌ NO hacer:
- No crear apps separadas para Emma
- No duplicar funcionalidad de AgenticSeek
- No usar puertos diferentes para AgenticSeek
- No hacer mock de agentes (usar implementación real)

### ✅ SÍ hacer:
- Usar la integración existente
- Reutilizar componentes de AgenticSeek
- Mantener experiencia unificada en Emma Studio
- Usar servicios cloud configurados

## 🔍 Debugging Común

### Emma no responde:
1. Verificar API keys en `.env`
2. Verificar backend en puerto 8001
3. Revisar logs: `backend/.logs/provider.log`

### Screenshots fallan:
1. Verificar Browserless API key
2. Test: `python backend/test_cloud_browser.py`
3. Revisar: `backend/.screenshots/`

### Frontend no carga:
1. Verificar puerto 3002 libre
2. Verificar proxy en `client/vite.config.ts`
3. Reinstalar: `cd client && npm install`

## 🎯 Objetivo Final

Emma AI debe ser una experiencia **seamless** donde:
- ✅ Los usuarios ven un solo agente inteligente (Emma)
- ✅ Internamente, múltiples agentes especializados trabajan en equipo
- ✅ La interfaz muestra el "wow factor" de agentes colaborando
- ✅ Todo está integrado en Emma Studio (no apps separadas)
- ✅ Reemplaza completamente las agencias de marketing tradicionales

---

## 📞 Soporte Rápido

**¿Algo no funciona?**
1. Ejecutar: `python scripts/verify_emma_setup.py`
2. Revisar: `client/src/pages/EMMA_AI_README.md`
3. Verificar: Variables de entorno en `backend/.env`

**¿Necesitas modificar Emma?**
1. Frontend: `client/src/pages/emma-agenticseek-page.tsx`
2. Backend: `emma-integration/emma_orchestrator.py`
3. Agentes: `backend/app/agenticseek/sources/agents/`

**Emma AI está lista para revolucionar el marketing! 🚀**
