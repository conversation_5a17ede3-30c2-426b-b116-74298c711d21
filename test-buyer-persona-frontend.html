<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Buyer Persona Frontend</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #3018ef;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2515d6;
        }
        .result {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Buyer Persona Frontend</h1>
    
    <div class="test-section">
        <h2>1. Test API Generation</h2>
        <button onclick="testGeneration()">Generate Buyer Personas</button>
        <div id="generation-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Conversation Simulator</h2>
        <button onclick="testConversation()">Test Conversation API</button>
        <div id="conversation-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Geographic Analysis</h2>
        <button onclick="testGeographic()">Test Geographic API</button>
        <div id="geographic-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Frontend Navigation</h2>
        <button onclick="openBuyerPersonaGenerator()">Open Buyer Persona Generator</button>
        <button onclick="openConversationSimulator()">Open Conversation Simulator</button>
        <div id="navigation-result"></div>
    </div>

    <script>
        async function testGeneration() {
            const resultDiv = document.getElementById('generation-result');
            resultDiv.innerHTML = '<div class="result">Testing generation...</div>';
            
            try {
                const response = await fetch('/api/generate-buyer-personas', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        product_description: 'Una plataforma de marketing digital con IA para empresas',
                        num_personas: 3,
                        industry: 'Tecnología',
                        request_timestamp: Date.now(),
                        request_id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                resultDiv.innerHTML = `
                    <div class="success">✅ Generation successful!</div>
                    <div class="result">Generated ${data.buyer_personas.length} personas:
${data.buyer_personas.map(p => `- ${p.name} (${p.age} años, ${p.job.title})`).join('\n')}

Request ID: ${data.request_id}
Generation time: ${data.generation_time_seconds}s</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testConversation() {
            const resultDiv = document.getElementById('conversation-result');
            resultDiv.innerHTML = '<div class="result">Testing conversation...</div>';
            
            try {
                const response = await fetch('/api/v1/premium/conversation/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        persona_data: {
                            name: 'Ana García',
                            age: 32,
                            job: { title: 'Marketing Manager', industry: 'Technology' }
                        },
                        conversation_type: 'sales',
                        context: 'Test conversation'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                resultDiv.innerHTML = `
                    <div class="success">✅ Conversation started!</div>
                    <div class="result">Conversation ID: ${data.conversation.conversation_id}
Persona: ${data.conversation.persona_name}
Initial message: ${data.conversation.messages[0]?.message.substring(0, 100)}...</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function testGeographic() {
            const resultDiv = document.getElementById('geographic-result');
            resultDiv.innerHTML = '<div class="result">Testing geographic analysis...</div>';
            
            try {
                const response = await fetch('/api/v1/premium/geographic/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        persona_data: {
                            name: 'Ana García',
                            location: 'Madrid, España'
                        },
                        target_regions: ['España']
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                resultDiv.innerHTML = `
                    <div class="success">✅ Geographic analysis complete!</div>
                    <div class="result">Analyzed regions: ${data.analyzed_regions.join(', ')}
Cultural context: ${data.regional_analysis.España.cultural_context.business_culture}
Preferred channels: ${data.regional_analysis.España.communication_preferences.preferred_channels.join(', ')}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        function openBuyerPersonaGenerator() {
            const resultDiv = document.getElementById('navigation-result');
            const url = '/dashboard/herramientas/buyer-persona-generator';
            resultDiv.innerHTML = `<div class="result">Opening: ${url}</div>`;
            window.open(url, '_blank');
        }

        function openConversationSimulator() {
            const resultDiv = document.getElementById('navigation-result');
            const url = '/dashboard/herramientas/buyer-persona-generator/simulador/test-persona';
            resultDiv.innerHTML = `<div class="result">Opening: ${url}</div>`;
            window.open(url, '_blank');
        }
    </script>
</body>
</html>
