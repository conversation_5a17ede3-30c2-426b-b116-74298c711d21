const fs = require('fs');
const path = require('path');

// Rutas de origen y destino
const source = '/Users/<USER>/Desktop/emma-studio--main/Imagen infogra.png';
const destination = '/Users/<USER>/Desktop/emma-studio--main/client/public/Imagen infogra.png';

try {
    // Verificar que el archivo origen existe
    if (!fs.existsSync(source)) {
        console.log('❌ Error: El archivo origen no existe:', source);
        process.exit(1);
    }

    // Copiar el archivo
    fs.copyFileSync(source, destination);
    
    // Verificar que se copió correctamente
    if (fs.existsSync(destination)) {
        const stats = fs.statSync(destination);
        console.log('✅ Imagen copiada exitosamente');
        console.log('📁 Origen:', source);
        console.log('📁 Destino:', destination);
        console.log('📊 Tamaño:', stats.size, 'bytes');
    } else {
        console.log('❌ Error: El archivo no se copió correctamente');
    }
} catch (error) {
    console.log('❌ Error al copiar la imagen:', error.message);
}
