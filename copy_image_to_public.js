const fs = require('fs');
const path = require('path');

// Rutas de origen y destino
const source = '/Users/<USER>/Desktop/emma-studio--main/Imagen infogra.png';
const destination = '/Users/<USER>/Desktop/emma-studio--main/client/public/imagen-infogra.png';

console.log('🔄 Copiando imagen de infografía...');
console.log('📁 Origen:', source);
console.log('📁 Destino:', destination);

try {
    // Verificar que el archivo origen existe
    if (!fs.existsSync(source)) {
        console.log('❌ Error: El archivo origen no existe:', source);
        process.exit(1);
    }

    // Crear directorio de destino si no existe
    const destDir = path.dirname(destination);
    if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
        console.log('📁 Directorio creado:', destDir);
    }

    // Copiar el archivo
    fs.copyFileSync(source, destination);
    
    // Verificar que se copió correctamente
    if (fs.existsSync(destination)) {
        const stats = fs.statSync(destination);
        console.log('✅ Imagen copiada exitosamente');
        console.log('📊 Tamaño:', stats.size, 'bytes');
        console.log('🎯 La imagen ahora está disponible en:', destination);
    } else {
        console.log('❌ Error: El archivo no se copió correctamente');
        process.exit(1);
    }
} catch (error) {
    console.log('❌ Error al copiar la imagen:', error.message);
    process.exit(1);
}

console.log('🎉 Proceso completado exitosamente');
