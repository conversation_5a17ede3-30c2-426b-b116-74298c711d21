/**
 * Safari-specific fixes and utilities
 * Addresses various Safari quirks with SPAs
 */

export const isSafari = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('safari') && !userAgent.includes('chrome') && !userAgent.includes('chromium');
};

export const isMobileSafari = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('safari') && userAgent.includes('mobile');
};

/**
 * Apply Safari-specific fixes on app initialization
 */
export const applySafariFixes = () => {
  if (!isSafari()) return;

  console.log('Safari detected, applying compatibility fixes...');

  // Fix 1: Prevent Safari from caching pages aggressively
  window.addEventListener('pageshow', (event) => {
    if (event.persisted) {
      console.log('Safari page cache detected, forcing reload');
      window.location.reload();
    }
  });

  // Fix 2: <PERSON>le <PERSON>'s back/forward button behavior
  window.addEventListener('popstate', (event) => {
    console.log('Safari popstate event detected', event.state);
    // Force a small delay to let wouter process the change
    setTimeout(() => {
      if (window.location.pathname !== window.history.state?.path) {
        console.log('Safari navigation mismatch, forcing sync');
        window.dispatchEvent(new PopStateEvent('popstate', { state: event.state }));
      }
    }, 10);
  });

  // Fix 3: Prevent Safari from blocking programmatic navigation
  const originalPushState = window.history.pushState;
  const originalReplaceState = window.history.replaceState;

  window.history.pushState = function(state, title, url) {
    console.log('Safari pushState intercepted:', url);
    originalPushState.call(this, state, title, url);
    
    // Dispatch a custom event to notify wouter
    window.dispatchEvent(new CustomEvent('safariNavigation', { 
      detail: { url, state, type: 'push' } 
    }));
  };

  window.history.replaceState = function(state, title, url) {
    console.log('Safari replaceState intercepted:', url);
    originalReplaceState.call(this, state, title, url);
    
    // Dispatch a custom event to notify wouter
    window.dispatchEvent(new CustomEvent('safariNavigation', { 
      detail: { url, state, type: 'replace' } 
    }));
  };

  // Fix 4: Handle Safari's viewport issues
  const handleViewportChange = () => {
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport && isMobileSafari()) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    }
  };

  handleViewportChange();
  window.addEventListener('orientationchange', handleViewportChange);

  console.log('Safari fixes applied successfully');
};

/**
 * Force navigation in Safari using multiple methods
 */
export const safariForceNavigate = (path: string): void => {
  console.log(`Safari force navigate to: ${path}`);
  
  try {
    // Method 1: Try history API
    window.history.pushState({}, '', path);
    window.dispatchEvent(new PopStateEvent('popstate'));
    
    // Method 2: Fallback to location change
    setTimeout(() => {
      if (window.location.pathname !== path) {
        console.log('History API failed, using location.href');
        window.location.href = path;
      }
    }, 100);
    
  } catch (error) {
    console.error('Safari navigation error:', error);
    window.location.href = path;
  }
};

/**
 * Create a Safari-compatible link handler
 */
export const createSafariLinkHandler = (path: string) => {
  return (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (isSafari()) {
      safariForceNavigate(path);
    } else {
      window.location.href = path;
    }
  };
};
