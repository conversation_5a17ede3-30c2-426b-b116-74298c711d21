import { useLocation } from "wouter";
import { useCallback } from "react";

/**
 * Custom hook for Safari-compatible navigation
 * Addresses Safari's specific issues with SPA routing
 */
export const useSafariNavigation = () => {
  const [, navigate] = useLocation();

  const safariNavigate = useCallback((path: string) => {
    console.log(`Attempting navigation to: ${path}`);
    
    try {
      // Method 1: Standard wouter navigation
      navigate(path);
      console.log("Wouter navigation attempted");
      
      // Method 2: Safari fallback with history API
      setTimeout(() => {
        if (window.location.pathname !== path) {
          console.log("Wouter failed, trying history.pushState");
          window.history.pushState({}, '', path);
          
          // Trigger a popstate event to notify wouter
          window.dispatchEvent(new PopStateEvent('popstate', { state: {} }));
        }
      }, 50);
      
      // Method 3: Force navigation if still not working
      setTimeout(() => {
        if (window.location.pathname !== path) {
          console.log("History API failed, using window.location.href");
          window.location.href = path;
        }
      }, 200);
      
      // Method 4: Emergency fallback
      setTimeout(() => {
        if (window.location.pathname !== path) {
          console.log("All methods failed, forcing page reload");
          window.location.replace(path);
        }
      }, 1000);
      
    } catch (error) {
      console.error("Navigation error:", error);
      // Emergency fallback
      window.location.href = path;
    }
  }, [navigate]);

  const forceNavigate = useCallback((path: string) => {
    console.log(`Force navigating to: ${path}`);
    window.location.href = path;
  }, []);

  return {
    navigate: safariNavigate,
    forceNavigate
  };
};

/**
 * Alternative navigation method using anchor tags
 * Sometimes works better in Safari than programmatic navigation
 */
export const createSafariLink = (path: string, onClick?: () => void) => {
  return {
    href: path,
    onClick: (e: React.MouseEvent) => {
      e.preventDefault();
      onClick?.();
      
      // Try multiple navigation methods
      setTimeout(() => {
        window.location.href = path;
      }, 10);
    }
  };
};
