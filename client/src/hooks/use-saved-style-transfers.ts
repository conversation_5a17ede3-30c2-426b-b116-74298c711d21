/**
 * Hook personalizado para manejar imágenes de transferencia de estilo guardadas
 * Sigue el mismo patrón que otros hooks de guardado
 */

import { useState, useEffect, useCallback } from 'react';

export interface SavedStyleTransfer {
  id: string;
  initImageUrl: string;
  styleImageUrl: string;
  processedUrl: string;
  initImageFilename: string;
  styleImageFilename: string;
  prompt?: string;
  negativePrompt?: string;
  styleStrength: number;
  compositionFidelity: number;
  changeStrength: number;
  seed?: number;
  outputFormat: string;
  timestamp: number;
  isFavorite: boolean;
}

const SAVED_STYLE_TRANSFERS_KEY = 'emma_saved_style_transfers';
const MAX_SAVED_ITEMS = 50;

export function useSavedStyleTransfers() {
  const [savedStyleTransfers, setSavedStyleTransfers] = useState<SavedStyleTransfer[]>([]);

  // Cargar datos guardados al inicializar
  useEffect(() => {
    try {
      const saved = localStorage.getItem(SAVED_STYLE_TRANSFERS_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setSavedStyleTransfers(Array.isArray(parsed) ? parsed : []);
      }
    } catch (error) {
      console.error('Error loading saved style transfers:', error);
      setSavedStyleTransfers([]);
    }
  }, []);

  // Guardar en localStorage cuando cambie el estado
  const saveToStorage = useCallback((transfers: SavedStyleTransfer[]) => {
    try {
      localStorage.setItem(SAVED_STYLE_TRANSFERS_KEY, JSON.stringify(transfers));
    } catch (error) {
      console.error('Error saving style transfers to localStorage:', error);
    }
  }, []);

  // Agregar nueva transferencia de estilo a favoritos
  const addToSaved = useCallback((styleTransferData: Omit<SavedStyleTransfer, 'id' | 'timestamp' | 'isFavorite'>) => {
    const newStyleTransfer: SavedStyleTransfer = {
      ...styleTransferData,
      id: `style_transfer_${Date.now()}_${Math.floor(Math.random() * 1000000)}`,
      timestamp: Date.now(),
      isFavorite: true,
    };

    setSavedStyleTransfers(prev => {
      const updated = [newStyleTransfer, ...prev].slice(0, MAX_SAVED_ITEMS);
      saveToStorage(updated);
      return updated;
    });

    return newStyleTransfer.id;
  }, [saveToStorage]);

  // Eliminar de favoritos
  const removeFromSaved = useCallback((id: string) => {
    setSavedStyleTransfers(prev => {
      const updated = prev.filter(transfer => transfer.id !== id);
      saveToStorage(updated);
      return updated;
    });
  }, [saveToStorage]);

  // Verificar si una imagen está guardada
  const isStyleTransferSaved = useCallback((imageUrl: string): boolean => {
    return savedStyleTransfers.some(transfer => transfer.processedUrl === imageUrl);
  }, [savedStyleTransfers]);

  // Obtener transferencia guardada por URL de imagen
  const getSavedStyleTransfer = useCallback((imageUrl: string): SavedStyleTransfer | undefined => {
    return savedStyleTransfers.find(transfer => transfer.processedUrl === imageUrl);
  }, [savedStyleTransfers]);

  // Limpiar todas las transferencias guardadas
  const clearAllSaved = useCallback(() => {
    setSavedStyleTransfers([]);
    saveToStorage([]);
  }, [saveToStorage]);

  // Actualizar transferencia existente
  const updateSavedStyleTransfer = useCallback((id: string, updates: Partial<SavedStyleTransfer>) => {
    setSavedStyleTransfers(prev => {
      const updated = prev.map(transfer => 
        transfer.id === id ? { ...transfer, ...updates } : transfer
      );
      saveToStorage(updated);
      return updated;
    });
  }, [saveToStorage]);

  return {
    savedStyleTransfers,
    setSavedStyleTransfers,
    addToSaved,
    removeFromSaved,
    isStyleTransferSaved,
    getSavedStyleTransfer,
    clearAllSaved,
    updateSavedStyleTransfer,
    totalSaved: savedStyleTransfers.length,
    maxSaved: MAX_SAVED_ITEMS,
    canSaveMore: savedStyleTransfers.length < MAX_SAVED_ITEMS,
  };
}
