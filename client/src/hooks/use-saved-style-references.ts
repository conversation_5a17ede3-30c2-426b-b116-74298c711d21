/**
 * Hook para manejar imágenes guardadas de style reference
 * Basado en el patrón de otros hooks de guardado
 */

import { useState, useEffect } from 'react';

const STORAGE_KEY = 'emma-studio-saved-style-references';

export interface SavedStyleReference {
  id: string;
  referenceUrl: string;
  processedUrl: string;
  referenceFilename: string;
  prompt: string;
  negativePrompt?: string;
  aspectRatio: string;
  fidelity: number;
  seed?: number;
  outputFormat: string;
  stylePreset?: string;
  timestamp: number;
  isFavorite: boolean;
}

export interface StyleReferenceData {
  referenceUrl: string;
  processedUrl: string;
  referenceFilename: string;
  prompt: string;
  negativePrompt?: string;
  aspectRatio: string;
  fidelity: number;
  seed?: number;
  outputFormat: string;
  stylePreset?: string;
}

export function useSavedStyleReferences() {
  const [savedStyleReferences, setSavedStyleReferences] = useState<SavedStyleReference[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Cargar imágenes guardadas del localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setSavedStyleReferences(parsed);
      }
    } catch (error) {
      console.error('Error loading saved style references:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Guardar en localStorage cuando cambie el estado
  useEffect(() => {
    if (!isLoading) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(savedStyleReferences));
      } catch (error) {
        console.error('Error saving style references to localStorage:', error);
      }
    }
  }, [savedStyleReferences, isLoading]);

  // Crear una nueva imagen guardada
  const createSavedStyleReference = (data: StyleReferenceData): SavedStyleReference => {
    return {
      id: `style_ref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...data,
      timestamp: Date.now(),
      isFavorite: true,
    };
  };

  // Agregar imagen a favoritos
  const addToSaved = (data: StyleReferenceData) => {
    const newStyleReference = createSavedStyleReference(data);
    setSavedStyleReferences(prev => [newStyleReference, ...prev].slice(0, 50)); // Limitar a 50
    return newStyleReference;
  };

  // Remover imagen de favoritos
  const removeFromSaved = (id: string) => {
    setSavedStyleReferences(prev => prev.filter(item => item.id !== id));
  };

  // Verificar si una imagen está guardada
  const isStyleReferenceSaved = (processedUrl: string): boolean => {
    return savedStyleReferences.some(item => item.processedUrl === processedUrl);
  };

  // Obtener imagen guardada por URL
  const getSavedStyleReference = (processedUrl: string): SavedStyleReference | undefined => {
    return savedStyleReferences.find(item => item.processedUrl === processedUrl);
  };

  // Limpiar todas las imágenes guardadas
  const clearSavedStyleReferences = () => {
    setSavedStyleReferences([]);
  };

  // Obtener imágenes ordenadas por timestamp (más recientes primero)
  const getSavedStyleReferencesSorted = (): SavedStyleReference[] => {
    return [...savedStyleReferences].sort((a, b) => b.timestamp - a.timestamp);
  };

  return {
    savedStyleReferences,
    isLoading,
    createSavedStyleReference,
    addToSaved,
    removeFromSaved,
    isStyleReferenceSaved,
    getSavedStyleReference,
    clearSavedStyleReferences,
    getSavedStyleReferencesSorted,
    setSavedStyleReferences,
    count: savedStyleReferences.length,
  };
}
