/**
 * Service for 3D model generation using Stability AI
 */

export interface Generate3DOptions {
  image: File;
  modelType?: 'fast' | 'point_aware';
  textureResolution?: '512' | '1024' | '2048';
  foregroundRatio?: number;
  remesh?: 'none' | 'triangle' | 'quad';
  vertexCount?: number;
  // Point Aware 3D specific options
  targetType?: 'none' | 'vertex' | 'face';
  targetCount?: number;
  guidanceScale?: number;
  seed?: number;
}

export interface Generate3DResponse {
  success: boolean;
  model_url?: string;
  filename?: string;
  size_mb?: number;
  metadata?: {
    texture_resolution: string;
    foreground_ratio: number;
    remesh: string;
    vertex_count: number;
    credits_used: number;
    generation_time: string;
    original_filename: string;
    content_type: string;
  };
  error?: string;
}

/**
 * Generate a 3D model from an image using Stability AI
 */
export async function generate3DModel(
  options: Generate3DOptions,
  progressCallback?: (progress: number, message: string) => void
): Promise<Generate3DResponse> {
  try {
    progressCallback?.(10, "Preparando imagen...");

    // Crear FormData
    const formData = new FormData();
    formData.append('image', options.image);

    if (options.modelType) {
      formData.append('model_type', options.modelType);
    }

    if (options.textureResolution) {
      formData.append('texture_resolution', options.textureResolution);
    }

    if (options.foregroundRatio !== undefined) {
      formData.append('foreground_ratio', options.foregroundRatio.toString());
    }

    if (options.remesh) {
      formData.append('remesh', options.remesh);
    }

    if (options.vertexCount !== undefined && options.vertexCount !== -1) {
      formData.append('vertex_count', options.vertexCount.toString());
    }

    // Point Aware 3D specific parameters
    if (options.targetType && options.targetType !== 'none') {
      formData.append('target_type', options.targetType);
    }

    if (options.targetCount !== undefined) {
      formData.append('target_count', options.targetCount.toString());
    }

    if (options.guidanceScale !== undefined) {
      formData.append('guidance_scale', options.guidanceScale.toString());
    }

    if (options.seed !== undefined && options.seed > 0) {
      formData.append('seed', options.seed.toString());
    }

    progressCallback?.(30, "Enviando a Stability AI...");

    // Llamar a la API
    const response = await fetch('/api/v1/images/generate-3d', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Error ${response.status}: ${response.statusText}`);
    }

    progressCallback?.(90, "Procesando modelo 3D...");

    const result: Generate3DResponse = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Error desconocido en la generación 3D');
    }

    progressCallback?.(100, "¡Modelo 3D generado exitosamente!");

    return result;

  } catch (error) {
    console.error('Error en generación 3D:', error);
    throw error;
  }
}

/**
 * Download a GLB file from a data URL
 */
export function downloadGLBFile(dataUrl: string, filename: string = 'model_3d.glb'): void {
  try {
    // Crear un enlace temporal para la descarga
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = filename;
    
    // Agregar al DOM, hacer clic y remover
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
  } catch (error) {
    console.error('Error al descargar archivo GLB:', error);
    throw new Error('No se pudo descargar el archivo');
  }
}

/**
 * Get file size in a human-readable format
 */
export function formatFileSize(sizeInMB: number): string {
  if (sizeInMB < 1) {
    return `${Math.round(sizeInMB * 1024)} KB`;
  } else {
    return `${sizeInMB.toFixed(2)} MB`;
  }
}

/**
 * Validate image file for 3D generation
 */
export function validateImageFor3D(file: File): { valid: boolean; error?: string } {
  // Verificar tipo de archivo
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Formato no soportado. Use JPEG, PNG o WebP.'
    };
  }

  // Verificar tamaño (máximo 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'El archivo es demasiado grande. Máximo 10MB.'
    };
  }

  return { valid: true };
}
