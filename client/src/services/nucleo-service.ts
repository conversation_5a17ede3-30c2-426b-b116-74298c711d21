import { supabase, Nucleo, CreateNucleoData, UpdateNucleoData } from '@/lib/supabase'

// Verificar si Supabase está disponible
const isSupabaseAvailable = () => {
  try {
    return import.meta.env.VITE_SUPABASE_URL &&
           import.meta.env.VITE_SUPABASE_URL !== 'https://placeholder.supabase.co'
  } catch {
    return false
  }
}

export class NucleoService {
  
  /**
   * Obtener todos los núcleos del usuario
   */
  static async getNucleos(userId?: string): Promise<Nucleo[]> {
    // Si Supabase no está disponible, devolver datos mock
    if (!isSupabaseAvailable()) {
      console.warn('Supabase not available, returning mock data')
      return []
    }

    try {
      let query = supabase
        .from('nucleos')
        .select('*')
        .order('updated_at', { ascending: false })

      if (userId) {
        query = query.eq('user_id', userId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching núcleos:', error)
        throw new Error(`Error al obtener núcleos: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error in getNucleos:', error)
      throw error
    }
  }

  /**
   * Obtener un núcleo por ID
   */
  static async getNucleoById(id: string): Promise<Nucleo | null> {
    try {
      const { data, error } = await supabase
        .from('nucleos')
        .select('*')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching núcleo:', error)
        throw new Error(`Error al obtener núcleo: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error in getNucleoById:', error)
      throw error
    }
  }

  /**
   * Crear un nuevo núcleo
   */
  static async createNucleo(nucleoData: CreateNucleoData): Promise<Nucleo> {
    try {
      const newNucleo = {
        ...nucleoData,
        status: 'draft' as const,
        campaigns_count: 0,
        assets_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('nucleos')
        .insert([newNucleo])
        .select()
        .single()

      if (error) {
        console.error('Error creating núcleo:', error)
        throw new Error(`Error al crear núcleo: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error in createNucleo:', error)
      throw error
    }
  }

  /**
   * Actualizar un núcleo existente
   */
  static async updateNucleo(nucleoData: UpdateNucleoData): Promise<Nucleo> {
    try {
      const { id, ...updateData } = nucleoData
      
      const updatedNucleo = {
        ...updateData,
        updated_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('nucleos')
        .update(updatedNucleo)
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating núcleo:', error)
        throw new Error(`Error al actualizar núcleo: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error in updateNucleo:', error)
      throw error
    }
  }

  /**
   * Eliminar un núcleo
   */
  static async deleteNucleo(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('nucleos')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting núcleo:', error)
        throw new Error(`Error al eliminar núcleo: ${error.message}`)
      }
    } catch (error) {
      console.error('Error in deleteNucleo:', error)
      throw error
    }
  }

  /**
   * Duplicar un núcleo
   */
  static async duplicateNucleo(id: string): Promise<Nucleo> {
    try {
      // Obtener el núcleo original
      const original = await this.getNucleoById(id)
      if (!original) {
        throw new Error('Núcleo no encontrado')
      }

      // Crear una copia con nuevo nombre
      const duplicateData: CreateNucleoData = {
        brand_name: `${original.brand_name} (Copia)`,
        website: original.website,
        industry: original.industry,
        logo_url: original.logo_url,
        primary_color: original.primary_color,
        secondary_color: original.secondary_color,
        target_audience: original.target_audience,
        tone: original.tone,
        personality: original.personality,
        description: original.description,
        unique_value: original.unique_value,
        competitors: original.competitors,
        documents: original.documents,
        examples: original.examples,
        user_id: original.user_id
      }

      return await this.createNucleo(duplicateData)
    } catch (error) {
      console.error('Error in duplicateNucleo:', error)
      throw error
    }
  }

  /**
   * Cambiar el estado de un núcleo
   */
  static async updateNucleoStatus(id: string, status: 'draft' | 'active' | 'archived'): Promise<Nucleo> {
    try {
      const { data, error } = await supabase
        .from('nucleos')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating núcleo status:', error)
        throw new Error(`Error al actualizar estado: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error in updateNucleoStatus:', error)
      throw error
    }
  }

  /**
   * Buscar núcleos por texto
   */
  static async searchNucleos(searchTerm: string, userId?: string): Promise<Nucleo[]> {
    try {
      let query = supabase
        .from('nucleos')
        .select('*')
        .or(`brand_name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,industry.ilike.%${searchTerm}%`)
        .order('updated_at', { ascending: false })

      if (userId) {
        query = query.eq('user_id', userId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error searching núcleos:', error)
        throw new Error(`Error en búsqueda: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error in searchNucleos:', error)
      throw error
    }
  }

  /**
   * Obtener estadísticas de núcleos
   */
  static async getNucleosStats(userId?: string): Promise<{
    total: number
    active: number
    draft: number
    archived: number
    totalCampaigns: number
    totalAssets: number
  }> {
    // Si Supabase no está disponible, devolver stats mock
    if (!isSupabaseAvailable()) {
      console.warn('Supabase not available, returning mock stats')
      return {
        total: 0,
        active: 0,
        draft: 0,
        archived: 0,
        totalCampaigns: 0,
        totalAssets: 0
      }
    }

    try {
      let query = supabase
        .from('nucleos')
        .select('status, campaigns_count, assets_count')

      if (userId) {
        query = query.eq('user_id', userId)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching stats:', error)
        throw new Error(`Error al obtener estadísticas: ${error.message}`)
      }

      const stats = {
        total: data?.length || 0,
        active: data?.filter(n => n.status === 'active').length || 0,
        draft: data?.filter(n => n.status === 'draft').length || 0,
        archived: data?.filter(n => n.status === 'archived').length || 0,
        totalCampaigns: data?.reduce((sum, n) => sum + (n.campaigns_count || 0), 0) || 0,
        totalAssets: data?.reduce((sum, n) => sum + (n.assets_count || 0), 0) || 0
      }

      return stats
    } catch (error) {
      console.error('Error in getNucleosStats:', error)
      throw error
    }
  }
}
