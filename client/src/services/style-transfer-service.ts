/**
 * Servicio para transferir estilo usando Stability AI
 * Basado en el patrón de otros servicios de Stability
 */

export interface StyleTransferOptions {
  initImage: File;
  styleImage: File;
  prompt?: string;
  negativePrompt?: string;
  styleStrength?: number; // 0.0-1.0
  compositionFidelity?: number; // 0.0-1.0
  changeStrength?: number; // 0.1-1.0
  seed?: number;
  outputFormat?: "jpeg" | "png" | "webp";
}

export interface StyleTransferResponse {
  success: boolean;
  image: string;
  seed?: number;
  finish_reason: string;
  error?: string;
  metadata?: {
    init_image_filename: string;
    style_image_filename: string;
    style_strength: number;
    composition_fidelity: number;
    change_strength: number;
  };
}

/**
 * Función para traducir prompts al inglés (requerido por Stability AI)
 */
async function translatePrompt(text: string): Promise<string> {
  if (!text || text.trim() === '') return '';
  
  try {
    const response = await fetch('/api/v1/translate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: text,
        target_language: 'en'
      }),
    });

    if (!response.ok) {
      console.warn('Translation failed, using original text');
      return text;
    }

    const data = await response.json();
    return data.translated_text || text;
  } catch (error) {
    console.warn('Translation error, using original text:', error);
    return text;
  }
}

/**
 * Transfiere el estilo de una imagen a otra
 */
export async function transferStyle(
  options: StyleTransferOptions
): Promise<string> {
  try {
    console.log("🎨 Iniciando transferencia de estilo:", {
      initImageName: options.initImage.name,
      initImageSize: `${Math.round(options.initImage.size / 1024)} KB`,
      styleImageName: options.styleImage.name,
      styleImageSize: `${Math.round(options.styleImage.size / 1024)} KB`,
      prompt: options.prompt || "",
      styleStrength: options.styleStrength || 1.0,
      compositionFidelity: options.compositionFidelity || 0.9,
      changeStrength: options.changeStrength || 0.9
    });

    // Traducir prompts al inglés
    const translatedPrompt = options.prompt 
      ? await translatePrompt(options.prompt)
      : "";
    const translatedNegativePrompt = options.negativePrompt
      ? await translatePrompt(options.negativePrompt)
      : undefined;

    console.log("🌐 Prompts traducidos:", {
      original: options.prompt,
      translated: translatedPrompt,
      originalNegative: options.negativePrompt,
      translatedNegative: translatedNegativePrompt
    });

    // Crear FormData
    const formData = new FormData();
    formData.append('init_image', options.initImage);
    formData.append('style_image', options.styleImage);
    formData.append('prompt', translatedPrompt);
    
    if (translatedNegativePrompt) {
      formData.append('negative_prompt', translatedNegativePrompt);
    }
    
    formData.append('style_strength', (options.styleStrength || 1.0).toString());
    formData.append('composition_fidelity', (options.compositionFidelity || 0.9).toString());
    formData.append('change_strength', (options.changeStrength || 0.9).toString());
    formData.append('seed', (options.seed || 0).toString());
    formData.append('output_format', options.outputFormat || 'png');

    console.log("📤 Enviando solicitud a Stability AI...");

    const response = await fetch('/api/v1/images/style-transfer', {
      method: 'POST',
      body: formData,
    });

    console.log("📥 Respuesta recibida:", response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Error del servidor:", errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { detail: errorText };
      }

      throw new Error(errorData.detail || `Error ${response.status}: ${response.statusText}`);
    }

    const data: StyleTransferResponse = await response.json();
    console.log("📊 Datos recibidos:", data);

    if (!data.success) {
      throw new Error(data.error || 'Error en la transferencia de estilo');
    }

    // Crear URL de la imagen desde base64
    const imageUrl = `data:image/${options.outputFormat || 'png'};base64,${data.image}`;
    console.log("✅ Transferencia de estilo completada exitosamente");

    return imageUrl;

  } catch (error) {
    console.error("❌ Error en transferencia de estilo:", error);
    throw error;
  }
}

/**
 * Función con progreso para usar con el contexto de tareas en background
 */
export async function transferStyleWithProgress(
  options: StyleTransferOptions,
  updateTask: (taskId: string, update: any) => void,
  taskId: string,
  keepInOriginPage?: boolean
): Promise<string> {
  // Crear tarea en background
  updateTask(taskId, {
    status: "processing",
    progress: 0,
    message: "Iniciando transferencia de estilo...",
    data: {
      type: "style-transfer",
      initImageName: options.initImage.name,
      styleImageName: options.styleImage.name,
      keepInOriginPage: keepInOriginPage || false
    }
  });

  // Simular progreso
  updateTask(taskId, { progress: 10, message: "Traduciendo prompts..." });
  
  await new Promise(resolve => setTimeout(resolve, 500));
  updateTask(taskId, { progress: 25, message: "Procesando imagen original..." });
  
  await new Promise(resolve => setTimeout(resolve, 500));
  updateTask(taskId, { progress: 40, message: "Analizando imagen de estilo..." });
  
  await new Promise(resolve => setTimeout(resolve, 500));
  updateTask(taskId, { progress: 60, message: "Transfiriendo estilo con IA..." });

  // Ejecutar la transferencia real
  const imageUrl = await transferStyle(options);

  // Completar la tarea
  updateTask(taskId, {
    status: "completed",
    progress: 100,
    message: "¡Estilo transferido exitosamente!",
    result: imageUrl
  });

  return imageUrl;
}
