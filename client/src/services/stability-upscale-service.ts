/**
 * Servicio para mejorar la calidad de imágenes usando Stability AI v2beta API
 * Soporta tres modos: Fast, Conservative, y Creative
 */

export interface UpscaleOptions {
  image: File;
  mode: "fast" | "conservative" | "creative";
  prompt?: string;
  negativePrompt?: string;
  seed?: number;
  outputFormat?: "jpeg" | "png" | "webp";
  creativity?: number;
  stylePreset?: string;
}

export interface UpscaleResponse {
  success: boolean;
  url?: string;
  seed?: number;
  finishReason?: string;
  mode?: string;
  error?: string;
}

export interface UpscaleStatusResponse {
  success: boolean;
  status?: "IN_PROGRESS" | "COMPLETED" | "FAILED";
  url?: string;
  seed?: number;
  finishReason?: string;
  error?: string;
}

export interface UpscaleModeInfo {
  id: string;
  name: string;
  description: string;
  credits: number;
  max_resolution: string;
  requires_prompt: boolean;
  processing_time: string;
}

export interface StylePresetInfo {
  id: string;
  name: string;
}

/**
 * Mejora la calidad de una imagen usando Stability AI
 */
export async function upscaleImage(options: UpscaleOptions): Promise<UpscaleResponse> {
  try {
    console.log(`🔧 Iniciando upscale con modo: ${options.mode}`);

    // Validar parámetros según el modo
    if ((options.mode === "conservative" || options.mode === "creative") && !options.prompt) {
      throw new Error(`El modo ${options.mode} requiere un prompt`);
    }

    // Preparar FormData
    const formData = new FormData();
    formData.append("image", options.image);
    formData.append("mode", options.mode);
    formData.append("output_format", options.outputFormat || "webp");

    // Agregar parámetros opcionales
    if (options.prompt) {
      formData.append("prompt", options.prompt);
    }
    if (options.negativePrompt) {
      formData.append("negative_prompt", options.negativePrompt);
    }
    if (options.seed && options.seed > 0) {
      formData.append("seed", options.seed.toString());
    }
    if (options.creativity) {
      formData.append("creativity", options.creativity.toString());
    }
    if (options.stylePreset) {
      formData.append("style_preset", options.stylePreset);
    }

    // Realizar petición
    const response = await fetch("/api/v1/ai-editor/upscale", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Convertir imagen base64 a data URL
    const imageUrl = `data:image/${options.outputFormat || "webp"};base64,${data.image}`;

    console.log(`✅ Upscale completado exitosamente - Modo: ${data.mode}`);

    return {
      success: true,
      url: imageUrl,
      seed: data.seed,
      finishReason: data.finish_reason,
      mode: data.mode,
    };
  } catch (error) {
    console.error("❌ Error en upscale:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido",
    };
  }
}

/**
 * Inicia un upscale creativo (asíncrono)
 */
export async function startCreativeUpscale(options: UpscaleOptions): Promise<{ success: boolean; generationId?: string; error?: string }> {
  try {
    console.log("🎨 Iniciando upscale creativo asíncrono");

    if (!options.prompt) {
      throw new Error("El modo creativo requiere un prompt");
    }

    // Preparar FormData
    const formData = new FormData();
    formData.append("image", options.image);
    formData.append("prompt", options.prompt);
    formData.append("output_format", options.outputFormat || "webp");

    // Agregar parámetros opcionales
    if (options.negativePrompt) {
      formData.append("negative_prompt", options.negativePrompt);
    }
    if (options.seed && options.seed > 0) {
      formData.append("seed", options.seed.toString());
    }
    if (options.creativity) {
      formData.append("creativity", options.creativity.toString());
    }
    if (options.stylePreset) {
      formData.append("style_preset", options.stylePreset);
    }

    // Realizar petición
    const response = await fetch("/api/v1/ai-editor/upscale/creative/start", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    console.log(`🆔 Upscale creativo iniciado - ID: ${data.generation_id}`);

    return {
      success: true,
      generationId: data.generation_id,
    };
  } catch (error) {
    console.error("❌ Error iniciando upscale creativo:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido",
    };
  }
}

/**
 * Consulta el estado de un upscale creativo
 */
export async function checkUpscaleStatus(generationId: string): Promise<UpscaleStatusResponse> {
  try {
    console.log(`📊 Consultando estado del upscale: ${generationId}`);

    const response = await fetch(`/api/v1/ai-editor/upscale/status/${generationId}`, {
      method: "GET",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    console.log(`📈 Estado: ${data.status}`);

    return {
      success: data.success,
      status: data.status,
      url: data.image_url,
      seed: data.seed,
      finishReason: data.finish_reason,
      error: data.error,
    };
  } catch (error) {
    console.error("❌ Error consultando estado:", error);
    return {
      success: false,
      status: "FAILED",
      error: error instanceof Error ? error.message : "Error desconocido",
    };
  }
}

/**
 * Mejora la calidad de una imagen con polling automático para modo creativo
 */
export async function upscaleImageWithPolling(
  options: UpscaleOptions,
  onProgress?: (progress: number, message?: string) => void,
  maxAttempts: number = 30,
  pollInterval: number = 10000
): Promise<string> {
  try {
    // Para modos fast y conservative, usar endpoint directo
    if (options.mode === "fast" || options.mode === "conservative") {
      if (onProgress) onProgress(50, "Procesando imagen...");
      
      const result = await upscaleImage(options);
      
      if (!result.success || !result.url) {
        throw new Error(result.error || "Error al procesar la imagen");
      }
      
      if (onProgress) onProgress(100, "¡Imagen mejorada exitosamente!");
      return result.url;
    }

    // Para modo creativo, usar polling
    if (onProgress) onProgress(10, "Iniciando mejora creativa...");

    const startResult = await startCreativeUpscale(options);
    if (!startResult.success || !startResult.generationId) {
      throw new Error(startResult.error || "Error al iniciar el proceso");
    }

    const generationId = startResult.generationId;
    if (onProgress) onProgress(20, "Procesando imagen con IA...");

    // Polling para obtener el resultado
    for (let attempts = 1; attempts <= maxAttempts; attempts++) {
      // Esperar antes de consultar
      await new Promise(resolve => setTimeout(resolve, pollInterval));

      // Actualizar progreso
      const progress = Math.min(20 + (attempts / maxAttempts) * 70, 90);
      if (onProgress) {
        onProgress(progress, `Mejorando calidad... (${attempts}/${maxAttempts})`);
      }

      // Consultar estado
      console.log(`Intento ${attempts}/${maxAttempts} - Consultando estado del ID: ${generationId}`);
      const statusResponse = await checkUpscaleStatus(generationId);

      // Si sigue en progreso, continuar con el polling
      if (statusResponse.status === "IN_PROGRESS") {
        continue;
      }

      // Si hay un error, lanzar excepción
      if (!statusResponse.success) {
        throw new Error(statusResponse.error || "Error al procesar la imagen");
      }

      // Si se completó exitosamente, devolver URL
      if (statusResponse.url) {
        // Marcar como 100% completo con mensaje de éxito
        if (onProgress) {
          onProgress(100, "¡Imagen mejorada exitosamente!");
        }

        return statusResponse.url;
      }
    }

    // Si llegamos aquí, se agotaron los intentos
    throw new Error("Tiempo de espera agotado. El proceso puede estar tomando más tiempo del esperado.");

  } catch (error) {
    console.error("❌ Error en upscale con polling:", error);
    throw error;
  }
}

/**
 * Obtiene información sobre los modos de upscale disponibles
 */
export async function getUpscaleModes(): Promise<{ modes: UpscaleModeInfo[]; stylePresets: StylePresetInfo[] }> {
  try {
    const response = await fetch("/api/v1/ai-editor/upscale/modes", {
      method: "GET",
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("❌ Error obteniendo modos de upscale:", error);
    // Devolver datos por defecto en caso de error
    return {
      modes: [
        {
          id: "fast",
          name: "Rápido",
          description: "Mejora 4x la resolución en ~1 segundo",
          credits: 1,
          max_resolution: "4x original",
          requires_prompt: false,
          processing_time: "~1 segundo"
        },
        {
          id: "conservative",
          name: "Conservador",
          description: "Mejora hasta 4K preservando aspectos originales",
          credits: 25,
          max_resolution: "4K",
          requires_prompt: true,
          processing_time: "~30-60 segundos"
        },
        {
          id: "creative",
          name: "Creativo",
          description: "Mejora hasta 4K reimaginando la imagen",
          credits: 25,
          max_resolution: "4K",
          requires_prompt: true,
          processing_time: "~2-5 minutos"
        }
      ],
      stylePresets: [
        { id: "enhance", name: "Mejorar" },
        { id: "photographic", name: "Fotográfico" },
        { id: "digital-art", name: "Arte Digital" }
      ]
    };
  }
}
