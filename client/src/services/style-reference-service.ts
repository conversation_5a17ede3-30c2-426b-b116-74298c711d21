/**
 * Servicio para aplicar estilo de referencia usando Stability AI
 * Basado en el patrón de otros servicios de Stability
 */

export interface StyleReferenceOptions {
  image: File;
  prompt: string;
  negativePrompt?: string;
  aspectRatio?: "16:9" | "1:1" | "21:9" | "2:3" | "3:2" | "4:5" | "5:4" | "9:16" | "9:21";
  fidelity?: number; // 0.0-1.0
  seed?: number;
  outputFormat?: "jpeg" | "png" | "webp";
  stylePreset?: 
    | "3d-model"
    | "analog-film"
    | "anime"
    | "cinematic"
    | "comic-book"
    | "digital-art"
    | "enhance"
    | "fantasy-art"
    | "isometric"
    | "line-art"
    | "low-poly"
    | "modeling-compound"
    | "neon-punk"
    | "origami"
    | "photographic"
    | "pixel-art"
    | "tile-texture";
}

export interface StyleReferenceResponse {
  success: boolean;
  image_url?: string;
  seed?: number;
  finish_reason?: string;
  metadata?: any;
  error?: string;
}

/**
 * Traduce un prompt del español al inglés para Stability AI
 */
async function translatePrompt(prompt: string): Promise<string> {
  try {
    // Por ahora, simplemente retornar el prompt original
    // TODO: Implementar traducción cuando el servicio esté disponible
    console.log("🌐 Usando prompt original (traducción deshabilitada temporalmente):", prompt);
    return prompt;
  } catch (error) {
    console.warn('Translation error, using original prompt:', error);
    return prompt;
  }
}

/**
 * Aplica estilo de referencia a una nueva imagen
 */
export async function applyStyleReference(
  options: StyleReferenceOptions
): Promise<string> {
  try {
    console.log("🎨 Iniciando aplicación de estilo de referencia:", {
      imageName: options.image.name,
      imageSize: `${Math.round(options.image.size / 1024)} KB`,
      prompt: options.prompt,
      fidelity: options.fidelity || 0.5,
      aspectRatio: options.aspectRatio || "1:1"
    });

    // Traducir prompt al inglés
    const translatedPrompt = await translatePrompt(options.prompt);
    const translatedNegativePrompt = options.negativePrompt
      ? await translatePrompt(options.negativePrompt)
      : undefined;

    console.log("🌐 Prompts traducidos:", {
      original: options.prompt,
      translated: translatedPrompt,
      originalNegative: options.negativePrompt,
      translatedNegative: translatedNegativePrompt
    });

    // Preparar FormData
    const formData = new FormData();
    formData.append('image', options.image);
    formData.append('prompt', translatedPrompt);

    if (translatedNegativePrompt) {
      formData.append('negative_prompt', translatedNegativePrompt);
    }

    formData.append('aspect_ratio', options.aspectRatio || '1:1');
    formData.append('fidelity', (options.fidelity || 0.5).toString());
    formData.append('seed', (options.seed || 0).toString());
    formData.append('output_format', options.outputFormat || 'png');

    if (options.stylePreset) {
      formData.append('style_preset', options.stylePreset);
    }

    console.log("📤 Enviando solicitud a Stability AI...");

    const response = await fetch('/api/v1/images/style-reference', {
      method: 'POST',
      body: formData,
    });

    console.log("📥 Respuesta recibida:", response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Error del servidor:", errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { detail: errorText };
      }

      throw new Error(errorData.detail || `Error ${response.status}: ${response.statusText}`);
    }

    const data: StyleReferenceResponse = await response.json();
    console.log("📊 Datos recibidos:", data);

    if (!data.success) {
      throw new Error(data.error || 'Error en la aplicación de estilo de referencia');
    }

    if (!data.image_url) {
      throw new Error('No se recibió imagen en la respuesta');
    }

    console.log("✅ Estilo de referencia aplicado exitosamente");
    return data.image_url;

  } catch (error) {
    console.error("❌ Error aplicando estilo de referencia:", error);
    throw error;
  }
}

/**
 * Función para usar con el sistema de tareas en segundo plano
 */
export async function applyStyleReferenceAsBackgroundTask(
  options: StyleReferenceOptions,
  addTask: (task: any) => string,
  updateTask: (taskId: string, updates: any) => void,
  originPage?: string,
  keepInOriginPage?: boolean
): Promise<string> {
  try {
    // Crear una tarea en segundo plano
    const taskId = addTask({
      type: "style-reference",
      status: "processing",
      progress: 0,
      message: "Preparando aplicación de estilo...",
      metadata: {
        prompt: options.prompt.substring(0, 100) + (options.prompt.length > 100 ? "..." : ""),
        aspectRatio: options.aspectRatio || "1:1",
        fidelity: options.fidelity || 0.5,
        taskTitle: "Aplicar estilo de referencia",
        originPage: originPage || "/style-reference",
        keepInOriginPage: keepInOriginPage || false
      }
    });

    // Simular progreso
    updateTask(taskId, { progress: 10, message: "Traduciendo prompt..." });
    
    await new Promise(resolve => setTimeout(resolve, 500));
    updateTask(taskId, { progress: 25, message: "Procesando imagen de referencia..." });
    
    await new Promise(resolve => setTimeout(resolve, 500));
    updateTask(taskId, { progress: 50, message: "Aplicando estilo con IA..." });

    // Ejecutar la generación real
    const imageUrl = await applyStyleReference(options);

    // Completar la tarea
    updateTask(taskId, {
      status: "completed",
      progress: 100,
      message: "¡Estilo aplicado exitosamente!",
      result: imageUrl
    });

    return imageUrl;

  } catch (error) {
    // Marcar la tarea como fallida
    updateTask(taskId!, {
      status: "failed",
      progress: 0,
      message: `Error: ${error instanceof Error ? error.message : 'Error desconocido'}`
    });
    
    throw error;
  }
}
