/**
 * Service for infographic generation and editing using OpenAI's gpt-image-1 model.
 * Supports initial generation, multi-turn editing, streaming, and mask-based editing.
 */

import { ensureFullImageUrl } from "./detect-deployment";

// Types for infographic operations
export interface InfographicGenerationOptions {
  prompt: string;
  size?: "1024x1024" | "1536x1024" | "1024x1536";
}

export interface MultiTurnEditOptions {
  previousResponseId: string;
  editPrompt: string;
}

export interface ReferenceEditOptions {
  prompt: string;
  referenceImages: File[];
  size?: "1024x1024" | "1536x1024" | "1024x1536";
}

export interface MaskEditOptions {
  prompt: string;
  image: File;
  mask: File;
}

export interface InfographicResponse {
  success: boolean;
  images?: string[];
  image_url?: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  error?: string;
}

export interface StreamResponse {
  success: boolean;
  partial_image?: boolean;
  image_url?: string;
  index?: number;
  progress?: number;
  final_image?: boolean;
  error?: string;
}

const API_BASE_URL = "/api/v1/infographics";

/**
 * Generate an initial infographic
 */
export async function generateInfographic(options: InfographicGenerationOptions): Promise<InfographicResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("size", options.size || "1024x1024");

    const response = await fetch(`${API_BASE_URL}/generate`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error generating infographic:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Edit an existing infographic using multi-turn generation
 */
export async function multiTurnEdit(options: MultiTurnEditOptions): Promise<InfographicResponse> {
  try {
    const formData = new FormData();
    formData.append("previous_response_id", options.previousResponseId);
    formData.append("edit_prompt", options.editPrompt);

    const response = await fetch(`${API_BASE_URL}/multi-turn-edit`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error in multi-turn edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Generate infographic with streaming partial images
 */
export async function* streamGeneration(prompt: string): AsyncGenerator<StreamResponse, void, unknown> {
  try {
    const formData = new FormData();
    formData.append("prompt", prompt);

    const response = await fetch(`${API_BASE_URL}/stream-generate`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body reader available");
    }

    const decoder = new TextDecoder();
    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6));
              
              // Ensure image URLs are properly formatted
              if (data.success && data.image_url) {
                data.image_url = ensureFullImageUrl(data.image_url);
              }
              
              yield data;
            } catch (parseError) {
              console.warn("Failed to parse streaming data:", parseError);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error("Error in streaming generation:", error);
    yield {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Generate infographic using reference images
 */
export async function editWithReferences(options: ReferenceEditOptions): Promise<InfographicResponse> {
  try {
    if (options.referenceImages.length > 4) {
      return {
        success: false,
        error: "Maximum 4 reference images allowed",
      };
    }

    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("size", options.size || "auto");

    options.referenceImages.forEach((image, index) => {
      formData.append("reference_images", image);
    });

    const response = await fetch(`${API_BASE_URL}/edit-with-references`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error in reference edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Edit infographic using a mask to specify areas to change
 */
export async function editWithMask(options: MaskEditOptions): Promise<InfographicResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("image", options.image);
    formData.append("mask", options.mask);

    const response = await fetch(`${API_BASE_URL}/edit-with-mask`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error in mask edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Utility function to validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ["image/jpeg", "image/png", "image/webp"];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: "Tipo de archivo no válido. Solo se permiten JPEG, PNG y WebP.",
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: "El archivo es demasiado grande. Máximo 10MB.",
    };
  }

  return { valid: true };
}
