import { imageApiService } from "./image-api-service";
import { ensureFullImageUrl } from "./detect-deployment";

// Tipos para la generación de logos
export interface LogoGenerationOptions {
  brandName: string; // Ahora contiene la descripción completa del logo
  industry: string;
  colorPreference: string;
  aspectRatio?: string;
  outputFormat?: "jpeg" | "png" | "webp";
  seed?: number;
}

export interface LogoGenerationRequest {
  prompt: string;
  negative_prompt?: string;
  aspect_ratio?: string;
  output_format?: "jpeg" | "png" | "webp";
  seed?: number;
  model: "ultra"; // Siempre usar Ultra para logos
}

/**
 * Genera el prompt optimizado para creación de logos
 * @param options Opciones de generación del logo
 * @returns Prompt optimizado para logos
 */
export function generateLogoPrompt(options: LogoGenerationOptions): string {
  // Combinar la descripción del usuario con mejoras específicas para logos
  const userDescription = options.brandName; // Ahora contiene la descripción completa

  // Agregar contexto de industria y colores si están especificados
  let enhancedPrompt = userDescription;

  // Agregar información de industria si está disponible
  if (options.industry && options.industry !== "other") {
    enhancedPrompt += `. This logo is for the ${options.industry} sector`;
  }

  // Agregar información de colores si está disponible
  if (options.colorPreference && options.colorPreference !== "neutral tones") {
    enhancedPrompt += `. Use a color scheme of ${options.colorPreference}`;
  }

  // Agregar mejoras específicas para logos
  enhancedPrompt += `. Create a professional, modern, and minimalist design with clean lines. The logo should be versatile and suitable for both digital and print use. Avoid clutter or excessive detail. Make it timeless and memorable.`;

  return enhancedPrompt;
}

/**
 * Genera el prompt negativo optimizado para logos
 * @returns Prompt negativo para evitar elementos no deseados en logos
 */
export function generateLogoNegativePrompt(): string {
  return "blurry, low quality, pixelated, cluttered, complex, busy, messy, unprofessional, amateur, distorted, watermark, text overlay, signature, copyright, low resolution, jpeg artifacts, noise, grain, oversaturated, too many colors, gradient abuse, drop shadow, bevel, emboss, 3d effects, realistic textures, photographic elements, people, faces, hands, animals unless relevant to brand";
}

/**
 * Genera un logo utilizando Stability AI Ultra
 * @param options Opciones de generación
 * @returns URL de la imagen generada
 */
export async function generateLogo(
  options: LogoGenerationOptions,
): Promise<string> {
  try {
    // Generar prompt optimizado para logos
    const prompt = generateLogoPrompt(options);
    const negativePrompt = generateLogoNegativePrompt();

    // Convertir opciones al formato esperado por el servicio de API
    const requestData: LogoGenerationRequest = {
      prompt,
      negative_prompt: negativePrompt,
      aspect_ratio: options.aspectRatio || "1:1",
      output_format: options.outputFormat || "webp",
      seed: options.seed,
      model: "ultra", // Siempre usar Ultra para mejor calidad en logos
    };

    // DEBUG: Log request data before sending
    console.log(
      "[DEBUG] Payload enviado a generateLogo:",
      requestData,
    );

    // Usar el servicio de API de imágenes para generar el logo
    const response = await imageApiService.generateImage(requestData);

    // Verificar respuesta
    if (!response.success) {
      throw new Error(response.error || "Error en la respuesta del servidor");
    }

    // Respuesta síncrona (imagen directa)
    if (
      response.tipo === "sincrono" &&
      response.images &&
      response.images.length > 0
    ) {
      // Asegurar URL absoluta
      const imageUrl = ensureFullImageUrl(response.images[0]);
      return imageUrl;
    }

    // Respuesta asíncrona (con ID de generación)
    if (response.tipo === "asincrono" && response.generationId) {
      // Esperar a que la imagen se genere (polling)
      const imageUrl = await pollForImageGeneration(response.generationId);
      return imageUrl;
    }

    // Si no coincide con ninguno de los formatos esperados
    throw new Error("Formato de respuesta no reconocido");
  } catch (error) {
    console.error("Error en el servicio de generación de logos:", error);
    throw error;
  }
}

/**
 * Genera un logo con seguimiento de progreso
 * @param options Opciones de generación
 * @param progressCallback Callback para reportar progreso
 * @returns URL de la imagen generada
 */
export async function generateLogoWithProgress(
  options: LogoGenerationOptions,
  progressCallback?: (progress: number, message?: string) => void,
): Promise<string> {
  try {
    // Reportar progreso inicial
    progressCallback?.(5, "Preparando generación de logo...");

    // Generar prompt optimizado para logos
    const prompt = generateLogoPrompt(options);
    const negativePrompt = generateLogoNegativePrompt();

    // Convertir opciones al formato esperado por el servicio de API
    const requestData: LogoGenerationRequest = {
      prompt,
      negative_prompt: negativePrompt,
      aspect_ratio: options.aspectRatio || "1:1",
      output_format: options.outputFormat || "webp",
      seed: options.seed,
      model: "ultra", // Siempre usar Ultra para mejor calidad en logos
    };

    // Reportar progreso
    progressCallback?.(10, "Enviando solicitud a Stability AI...");

    // Usar el servicio de API de imágenes para generar el logo
    const response = await imageApiService.generateImage(requestData);

    // Verificar respuesta
    if (!response.success) {
      throw new Error(response.error || "Error en la respuesta del servidor");
    }

    // Respuesta síncrona (imagen directa)
    if (
      response.tipo === "sincrono" &&
      response.images &&
      response.images.length > 0
    ) {
      // Asegurar URL absoluta
      const imageUrl = ensureFullImageUrl(response.images[0]);
      progressCallback?.(95, "Logo recibido, procesando...");
      progressCallback?.(100, "Logo generado con éxito");
      return imageUrl;
    }

    // Respuesta asíncrona (con ID de generación)
    if (response.tipo === "asincrono" && response.generationId) {
      // Obtener el ID de generación
      const generationId = response.generationId;

      // Reportar progreso
      progressCallback?.(15, "Generación iniciada, esperando resultados...");

      // Esperar a que la imagen se genere (polling con feedback de progreso)
      const imageUrl = await pollForImageGenerationWithProgress(
        generationId,
        progressCallback,
      );
      progressCallback?.(100, "Logo generado con éxito");
      return imageUrl;
    }

    // Si no coincide con ninguno de los formatos esperados
    throw new Error("Formato de respuesta no reconocido");
  } catch (error) {
    console.error("Error en el servicio de generación de logos:", error);
    throw error;
  }
}

// Funciones auxiliares

async function pollForImageGeneration(generationId: string): Promise<string> {
  // Implementación básica de polling
  // En una implementación real, esto debería hacer polling al endpoint de estado
  throw new Error("Polling asíncrono no implementado para logos");
}

async function pollForImageGenerationWithProgress(
  generationId: string,
  progressCallback?: (progress: number, message?: string) => void,
): Promise<string> {
  // Implementación básica de polling con progreso
  // En una implementación real, esto debería hacer polling al endpoint de estado
  throw new Error("Polling asíncrono con progreso no implementado para logos");
}

/**
 * Opciones predefinidas para industrias comunes
 */
export const INDUSTRY_OPTIONS = [
  { value: "technology", label: "Tecnología" },
  { value: "healthcare", label: "Salud y Medicina" },
  { value: "finance", label: "Finanzas y Banca" },
  { value: "education", label: "Educación" },
  { value: "retail", label: "Retail y Comercio" },
  { value: "food", label: "Alimentación y Restaurantes" },
  { value: "real-estate", label: "Bienes Raíces" },
  { value: "consulting", label: "Consultoría" },
  { value: "creative", label: "Creativo y Diseño" },
  { value: "fitness", label: "Fitness y Deportes" },
  { value: "beauty", label: "Belleza y Cosmética" },
  { value: "automotive", label: "Automotriz" },
  { value: "travel", label: "Viajes y Turismo" },
  { value: "legal", label: "Legal y Jurídico" },
  { value: "construction", label: "Construcción" },
  { value: "other", label: "Otro" },
];

/**
 * Opciones predefinidas para esquemas de color
 */
export const COLOR_OPTIONS = [
  { value: "neutral tones", label: "Tonos Neutros" },
  { value: "blue and white", label: "Azul y Blanco" },
  { value: "black and white", label: "Blanco y Negro" },
  { value: "green and blue", label: "Verde y Azul" },
  { value: "red and black", label: "Rojo y Negro" },
  { value: "purple and gold", label: "Púrpura y Dorado" },
  { value: "orange and blue", label: "Naranja y Azul" },
  { value: "monochromatic blue", label: "Azul Monocromático" },
  { value: "warm colors", label: "Colores Cálidos" },
  { value: "cool colors", label: "Colores Fríos" },
  { value: "vibrant colors", label: "Colores Vibrantes" },
  { value: "pastel colors", label: "Colores Pastel" },
];
