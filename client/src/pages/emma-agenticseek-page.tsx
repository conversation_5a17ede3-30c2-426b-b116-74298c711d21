import React from 'react';
import DashboardLayoutWrapper from '@/components/layout/dashboard-layout';
/**
 * 🤖 EMMA AI - AGENTE PRINCIPAL DE EMMA STUDIO
 *
 * ARQUITECTURA:
 * - Base: AgenticSeek (open source) integrado como componente
 * - Capa: <PERSON> (coordinación personalizada)
 * - Cloud: Browserless.io, Ser<PERSON>, Gemini APIs
 * - Agentes: CasualAgent, BrowserAgent, CoderAgent, PlannerAgent, FileAgent, McpAgent
 *
 * RUTAS DISPONIBLES:
 * - /emma-ai (principal)
 * - /emma-agenticseek (técnica)
 * - /dashboard/emma-ai (dashboard)
 *
 * DOCUMENTACIÓN: client/src/pages/EMMA_AI_README.md
 * ARQUITECTURA: docs/EMMA_AI_ARCHITECTURE.md
 */

// Importar directamente el componente de AgenticSeek integrado
// NOTA: Usando componente funcional temporal mientras se restaura el original
import AgenticSeekApp from '@/components/AgenticSeekApp';

/**
 * Contenedor principal de Emma AI
 * Renderiza la interfaz de AgenticSeek con funcionalidades cloud personalizadas
 */
function EmmaAgenticSeekContent() {
  return (
    <div className="h-full w-full">
      {/*
        AgenticSeekApp: Interfaz de chat multi-agente
        - Incluye todos los agentes especializados
        - Conecta con Emma Orchestrator en backend
        - Muestra "wow factor" visual (screenshots, progress)
      */}
      <AgenticSeekApp />
    </div>
  );
}

/**
 * Página principal de Emma AI
 * Integrada completamente en Emma Studio (no app separada)
 */
export default function EmmaAgenticSeekPage() {
  return (
    <DashboardLayoutWrapper pageTitle="Emma AI">
      <EmmaAgenticSeekContent />
    </DashboardLayoutWrapper>
  );
}
