import { withAuth } from "@/lib/with-auth"
import MoodBoardEditor from "@/components/tools/mood-board-editor"

interface MoodBoardEditorPageProps {
  boardId: string
}

function MoodBoardEditorPageContent({ boardId }: MoodBoardEditorPageProps) {
  return <MoodBoardEditor boardId={boardId} />
}

function MoodBoardEditorPage({ boardId }: MoodBoardEditorPageProps) {
  // No usamos DashboardLayoutWrapper porque queremos pantalla completa
  return <MoodBoardEditorPageContent boardId={boardId} />
}

// Solo para fines de desarrollo y visualización, luego volver a habilitar la autenticación
// export default withAuth(MoodBoardEditorPage);
export default MoodBoardEditorPage
