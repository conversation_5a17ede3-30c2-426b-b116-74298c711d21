# 🤖 Emma AI - Guía Rápida para Desarrolladores

## 🎯 ¿Qué es Emma AI?

Emma AI es el **agente principal** de Emma Studio, accesible en `http://localhost:5173/emma-ai`. Está construida sobre **AgenticSeek** (open source) con funcionalidades cloud personalizadas.

## 🚀 Acceso Rápido

### URLs Disponibles:
- **Principal**: `http://localhost:5173/emma-ai`
- **Técnica**: `http://localhost:5173/emma-agenticseek` 
- **Dashboard**: `http://localhost:5173/dashboard/emma-ai`

### Archivos Clave:
- **Interfaz**: `client/src/pages/emma-agenticseek-page.tsx`
- **Estilos**: `client/src/pages/emma-agenticseek.css`
- **Backend**: `backend/app/agenticseek/`
- **Orquestador**: `emma-integration/emma_orchestrator.py`

## 🏗️ Arquitectura Simplificada

```
Emma AI Interface (React)
    ↓
Emma Orchestrator (Python)
    ↓
AgenticSeek Core (Open Source)
    ↓
Specialized Agents (CasualAgent, BrowserAgent, etc.)
    ↓
Cloud Services (Browserless.io, Serper, etc.)
```

## 🔧 Configuración Rápida

### 1. Variables de Entorno
```bash
# En backend/.env
SERPER_API_KEY=2187e03c0d1710eeaa3e669daf6a4fcddc1b84cb
BROWSERLESS_API_KEY=2SP6LRG5ebh7ohfc3b60651a5f2f574bc95ee4e3c8caf2a58
GEMINI_API_KEY=tu_clave_gemini
```

### 2. Puertos
- **Frontend**: 3002 (Vite dev server)
- **Backend**: 8001 (FastAPI)
- **AgenticSeek**: Integrado en 8001

### 3. Testing
```bash
# Test básico
python backend/test_emma_agenticseek.py

# Test browser cloud
python backend/test_cloud_browser.py
```

## 🎨 Características Principales

### ✅ Multi-Agente Real
- **CasualAgent**: Conversación general
- **BrowserAgent**: Navegación web con screenshots
- **CoderAgent**: Programación y código
- **PlannerAgent**: Planificación de tareas complejas
- **FileAgent**: Gestión de archivos

### ✅ "Wow Factor" Visual
- Screenshots en tiempo real
- Visualización de agentes trabajando
- Progress indicators
- Chat unificado (no tabs)

### ✅ Cloud-First
- Browserless.io para screenshots
- Serper para búsquedas web
- Gemini para análisis de imágenes

## 🛠️ Modificaciones Comunes

### Cambiar interfaz de Emma:
```typescript
// Editar: client/src/pages/emma-agenticseek-page.tsx
export default function EmmaAgenticSeekPage() {
  return (
    <DashboardLayoutWrapper pageTitle="Emma AI">
      <EmmaAgenticSeekContent />
    </DashboardLayoutWrapper>
  );
}
```

### Agregar nuevo agente:
```python
# 1. Crear en: backend/app/agenticseek/sources/agents/mi_agente.py
# 2. Registrar en: emma-integration/emma_orchestrator.py
# 3. Prompt en: backend/app/agenticseek/prompts/base/mi_agente.txt
```

### Modificar orquestación:
```python
# Editar: emma-integration/emma_orchestrator.py
class EmmaOrchestrator:
    def delegate_task(self, task):
        # Tu lógica de delegación aquí
        pass
```

## 🚨 Puntos Críticos

### ❌ NO hacer:
- No crear apps separadas para Emma
- No duplicar funcionalidad de AgenticSeek
- No usar puertos diferentes para AgenticSeek
- No hacer mock de agentes (usar implementación real)

### ✅ SÍ hacer:
- Usar la integración existente
- Reutilizar componentes de AgenticSeek
- Mantener experiencia unificada
- Usar servicios cloud configurados

## 🔍 Debugging

### Logs importantes:
```bash
# Backend logs
tail -f backend/.logs/provider.log

# AgenticSeek logs  
tail -f backend/.logs/memory.log

# Browser screenshots
ls backend/.screenshots/
```

### Problemas comunes:
1. **Emma no responde**: Verificar API keys en .env
2. **Screenshots fallan**: Verificar Browserless.io key
3. **Agentes no funcionan**: Verificar AgenticSeek dependencies
4. **Frontend no carga**: Verificar puerto 3002 libre

## 📚 Documentación Completa

Para arquitectura detallada ver: `docs/EMMA_AI_ARCHITECTURE.md`

## 🎯 Objetivo Final

Emma AI debe ser una experiencia **seamless** donde:
- Los usuarios ven un solo agente inteligente (Emma)
- Internamente, múltiples agentes especializados trabajan en equipo
- La interfaz muestra el "wow factor" de agentes colaborando
- Todo está integrado en Emma Studio (no apps separadas)

---

**💡 Tip**: Si algo no funciona, primero verificar que todas las API keys estén configuradas y que AgenticSeek esté correctamente integrado.
