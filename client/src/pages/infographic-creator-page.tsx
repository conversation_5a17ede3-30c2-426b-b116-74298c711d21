import React, { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  BarChart3,
  Upload,
  Wand2,
  Download,
  Share2,
  Heart,
  Sparkles,
  Image as ImageIcon,
  Edit3,
  Layers,
  Palette,
  Type,
  TrendingUp,
  Users,
  Target,
  Zap,
  RefreshCw,
  Play,
  Pause,
  Settings,
  Info,
  X,
  Plus,
  Trash2,
  Eye,
  EyeOff,
  Brush
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { <PERSON>lide<PERSON> } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  generateInfographic,
  editWithReferences,
  editWithMask,
  validateImageFile,
  type InfographicGenerationOptions,
  type InfographicResponse
} from "@/services/infographic-service";

// Tipos para el estado de la aplicación
interface GeneratedInfographic {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  timestamp: number;
}

// Interfaz para infografías guardadas
interface SavedInfographic {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  metadata?: any;
  timestamp: number;
  isFavorite: boolean;
  type: "basic" | "reference" | "mask_edit";
}

// Custom hook para localStorage
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
        window.dispatchEvent(new CustomEvent('localStorage', {
          detail: { key, newValue: valueToStore }
        }));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

// Funciones para manejar infografías guardadas
const SAVED_INFOGRAPHICS_KEY = 'emma_saved_infographics';

const createSavedInfographic = (infographicData: Omit<SavedInfographic, 'id' | 'timestamp' | 'isFavorite'>): SavedInfographic => {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 1000000).toString();

  return {
    ...infographicData,
    id: `infographic_${timestamp}_${randomPart}`,
    timestamp: timestamp,
    isFavorite: true,
  };
};

const isInfographicSaved = (imageUrl: string, savedInfographics: SavedInfographic[]): boolean => {
  return savedInfographics.some(inf => inf.image_url === imageUrl);
};

const InfographicCreatorPage = () => {
  // Estados principales
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentInfographic, setCurrentInfographic] = useState<GeneratedInfographic | null>(null);
  const [size, setSize] = useState<"1024x1024" | "1536x1024" | "1024x1536">("1024x1024");

  // Estados para edición con referencias
  const [referenceImages, setReferenceImages] = useState<File[]>([]);
  const [referencePrompt, setReferencePrompt] = useState("");

  // Estados para edición con máscara integrada
  const [editingImage, setEditingImage] = useState<string | null>(null);
  const [maskPrompt, setMaskPrompt] = useState("");
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(20);
  const [showMask, setShowMask] = useState(false);

  // Estados de UI
  const [activeTab, setActiveTab] = useState("generate");
  const [mainTab, setMainTab] = useState("latest");

  // Estados para favoritos
  const [savedInfographics, setSavedInfographics] = useLocalStorage<SavedInfographic[]>(SAVED_INFOGRAPHICS_KEY, []);
  const [currentInfographicSaved, setCurrentInfographicSaved] = useState(false);

  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Prompts de ejemplo para infografías
  const examplePrompts = [
    "Infografía sobre los beneficios del trabajo remoto con estadísticas y gráficos coloridos",
    "Guía visual paso a paso para crear una estrategia de marketing digital efectiva",
    "Comparación visual entre diferentes tipos de energías renovables con datos y porcentajes",
    "Timeline de la evolución de la inteligencia artificial desde 1950 hasta 2024",
    "Infografía nutricional sobre los superalimentos más saludables del mundo",
    "Proceso de creación de una startup desde la idea hasta el lanzamiento"
  ];

  // Tamaños disponibles según documentación OpenAI
  const sizeOptions = [
    { value: "1024x1024", label: "Cuadrado (1024x1024)", icon: "⬜" },
    { value: "1536x1024", label: "Horizontal (1536x1024)", icon: "▭" },
    { value: "1024x1536", label: "Vertical (1024x1536)", icon: "▯" }
  ];

  // Función para generar infografía inicial
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Prompt requerido",
        description: "Por favor, describe la infografía que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const options: InfographicGenerationOptions = {
        prompt,
        size,
      };

      const result = await generateInfographic(options);

      if (result.success && result.image_url) {
        const newInfographic: GeneratedInfographic = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt,
          revised_prompt: result.revised_prompt,
          response_id: result.response_id,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentInfographic(newInfographic);

        toast({
          title: "¡Infografía generada!",
          description: "Tu infografía ha sido creada exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating infographic:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar la infografía",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para cargar imagen para editar
  const handleLoadImageForEdit = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      setEditingImage(imageUrl);

      // Cargar imagen en el canvas
      setTimeout(() => {
        const canvas = canvasRef.current;
        const maskCanvas = maskCanvasRef.current;
        if (canvas && maskCanvas) {
          const ctx = canvas.getContext('2d');
          const maskCtx = maskCanvas.getContext('2d');
          const img = new Image();

          img.onload = () => {
            // Calcular tamaño apropiado para el canvas (máximo 600px de ancho)
            const maxWidth = 600;
            const maxHeight = 400;
            let { width, height } = img;

            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }

            if (height > maxHeight) {
              width = (width * maxHeight) / height;
              height = maxHeight;
            }

            // Configurar tamaño del canvas
            canvas.width = width;
            canvas.height = height;
            maskCanvas.width = width;
            maskCanvas.height = height;

            // Aplicar tamaño CSS para que se vea correctamente
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            maskCanvas.style.width = `${width}px`;
            maskCanvas.style.height = `${height}px`;

            // Dibujar imagen original escalada
            ctx?.drawImage(img, 0, 0, width, height);

            // Limpiar máscara (fondo negro)
            if (maskCtx) {
              maskCtx.fillStyle = 'black';
              maskCtx.fillRect(0, 0, width, height);
            }
          };

          img.src = imageUrl;
        }
      }, 100);
    };
    reader.readAsDataURL(file);
  };

  // Funciones para dibujar en el canvas
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    draw(e);
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();

    // Calcular la escala entre el canvas real y el canvas mostrado
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    // Ajustar coordenadas según la escala
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = 'white';
    ctx.beginPath();
    ctx.arc(x, y, (brushSize / 2) * scaleX, 0, 2 * Math.PI);
    ctx.fill();
  };

  // Función para limpiar máscara
  const clearMask = () => {
    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  };

  // Función para manejar archivos de referencia
  const handleReferenceFiles = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      const validation = validateImageFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    if (errors.length > 0) {
      toast({
        title: "Archivos inválidos",
        description: errors.join(", "),
        variant: "destructive",
      });
    }

    if (validFiles.length > 0) {
      setReferenceImages(prev => [...prev, ...validFiles].slice(0, 4)); // Máximo 4 imágenes
    }
  };

  // Función para generar con referencias
  const handleGenerateWithReferences = async () => {
    if (!referencePrompt.trim() || referenceImages.length === 0) {
      toast({
        title: "Datos requeridos",
        description: "Necesitas un prompt y al menos una imagen de referencia",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const result = await editWithReferences({
        prompt: referencePrompt,
        referenceImages,
        size,
      });

      if (result.success && result.image_url) {
        const newInfographic: GeneratedInfographic = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt: referencePrompt,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentInfographic(newInfographic);

        toast({
          title: "¡Infografía generada!",
          description: "Tu infografía con referencias ha sido creada exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating with references:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar con referencias",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para editar con máscara integrada
  const handleEditWithMask = async () => {
    if (!maskPrompt.trim() || !editingImage) {
      toast({
        title: "Datos requeridos",
        description: "Necesitas cargar una imagen y escribir qué quieres cambiar",
        variant: "destructive",
      });
      return;
    }

    const canvas = canvasRef.current;
    const maskCanvas = maskCanvasRef.current;

    if (!canvas || !maskCanvas) {
      toast({
        title: "Error",
        description: "Canvas no disponible",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Convertir canvas a archivos
      const imageBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/png');
      });

      const maskBlob = await new Promise<Blob>((resolve) => {
        maskCanvas.toBlob((blob) => resolve(blob!), 'image/png');
      });

      // Crear archivos File
      const imageFile = new File([imageBlob], 'image.png', { type: 'image/png' });
      const maskFile = new File([maskBlob], 'mask.png', { type: 'image/png' });

      const result = await editWithMask({
        prompt: maskPrompt,
        image: imageFile,
        mask: maskFile,
      });

      if (result.success && result.image_url) {
        const newInfographic: GeneratedInfographic = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt: maskPrompt,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentInfographic(newInfographic);
        setMaskPrompt("");

        toast({
          title: "¡Infografía editada!",
          description: "Los cambios han sido aplicados exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error editing with mask:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al editar la infografía",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para descargar imagen
  const handleDownload = async () => {
    if (!currentInfographic?.image_url) return;

    try {
      const response = await fetch(currentInfographic.image_url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `infografia-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Descarga iniciada",
        description: "La infografía se está descargando",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo descargar la imagen",
        variant: "destructive",
      });
    }
  };

  // Función para compartir
  const handleShare = async () => {
    if (!currentInfographic?.image_url) return;

    try {
      if (navigator.share) {
        await navigator.share({
          title: "Mi Infografía",
          text: currentInfographic.prompt,
          url: currentInfographic.image_url,
        });
      } else {
        // Fallback: copiar al portapapeles
        await navigator.clipboard.writeText(currentInfographic.image_url);
        toast({
          title: "Enlace copiado",
          description: "El enlace de la infografía se copió al portapapeles",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo compartir la imagen",
        variant: "destructive",
      });
    }
  };

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!currentInfographic?.image_url) return;

    try {
      if (currentInfographicSaved) {
        // Quitar de favoritos
        const savedInfographic = savedInfographics.find(inf => inf.image_url === currentInfographic.image_url);
        if (savedInfographic) {
          const filteredInfographics = savedInfographics.filter(inf => inf.id !== savedInfographic.id);
          setSavedInfographics(filteredInfographics);
          setCurrentInfographicSaved(false);

          toast({
            title: "💔 Eliminada de favoritos",
            description: "La infografía ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const infographicData = {
          image_url: currentInfographic.image_url,
          prompt: currentInfographic.prompt,
          revised_prompt: currentInfographic.revised_prompt,
          metadata: currentInfographic.metadata,
          type: (currentInfographic.metadata?.type || "basic") as "basic" | "reference" | "mask_edit",
        };

        const newInfographic = createSavedInfographic(infographicData);
        const updatedInfographics = [newInfographic, ...savedInfographics].slice(0, 50); // Limitar a 50

        setSavedInfographics(updatedInfographics);
        setCurrentInfographicSaved(true);

        toast({
          title: "❤️ ¡Guardada en favoritos!",
          description: "Infografía guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar la infografía. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [currentInfographic, currentInfographicSaved, savedInfographics, setSavedInfographics, toast]);

  // Verificar si la infografía actual está guardada
  React.useEffect(() => {
    if (currentInfographic?.image_url) {
      setCurrentInfographicSaved(isInfographicSaved(currentInfographic.image_url, savedInfographics));
    }
  }, [currentInfographic, savedInfographics]);

  return (
    <DashboardLayout pageTitle="Crear Infografía">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Crear Infografía
                </h1>
              </div>
              <p className="text-xl text-blue-100 mb-6 max-w-3xl">
                Crea infografías profesionales con IA. Genera desde cero, usa referencias visuales o edita con precisión.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Sparkles className="w-3 h-3 mr-1" />
                  OpenAI GPT-Image-1
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Brush className="w-3 h-3 mr-1" />
                  Editor integrado
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <ImageIcon className="w-3 h-3 mr-1" />
                  Referencias visuales
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Edit3 className="w-3 h-3 mr-1" />
                  Edición precisa
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Tabs principales */}
        <Tabs value={mainTab} onValueChange={setMainTab} className="max-w-7xl mx-auto">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="latest">Última Generación</TabsTrigger>
            <TabsTrigger value="saved">
              Guardados ({savedInfographics.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="latest">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <div className="lg:col-span-1">
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Panel de Control
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="generate" className="text-xs">
                      <Wand2 className="h-3 w-3 mr-1" />
                      Generar
                    </TabsTrigger>
                    <TabsTrigger value="references" className="text-xs">
                      <ImageIcon className="h-3 w-3 mr-1" />
                      Referencias
                    </TabsTrigger>
                    <TabsTrigger value="edit" className="text-xs">
                      <Edit3 className="h-3 w-3 mr-1" />
                      Editar
                    </TabsTrigger>
                  </TabsList>

                  {/* Tab: Generar */}
                  <TabsContent value="generate" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción de la Infografía</Label>
                      <Textarea
                        placeholder="Describe la infografía que quieres crear..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Tamaño</Label>
                      <div className="grid grid-cols-1 gap-2">
                        {sizeOptions.map((option) => (
                          <Button
                            key={option.value}
                            variant={size === option.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSize(option.value as any)}
                            className="justify-start"
                          >
                            <span className="mr-2">{option.icon}</span>
                            {option.label}
                          </Button>
                        ))}
                      </div>
                    </div>



                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim()}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Generar Infografía
                        </>
                      )}
                    </Button>

                    {/* Prompts de ejemplo */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600">Ejemplos:</Label>
                      <div className="space-y-1">
                        {examplePrompts.slice(0, 3).map((example, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            onClick={() => setPrompt(example)}
                            className="w-full text-left text-xs h-auto p-2 justify-start"
                          >
                            {example}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </TabsContent>



                  {/* Tab: Referencias */}
                  <TabsContent value="references" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Prompt con Referencias</Label>
                      <Textarea
                        placeholder="Describe el tema de tu infografía. Las imágenes de referencia se analizarán automáticamente para aplicar su estilo visual..."
                        value={referencePrompt}
                        onChange={(e) => setReferencePrompt(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Tamaño</Label>
                      <div className="grid grid-cols-1 gap-2">
                        {sizeOptions.map((option) => (
                          <Button
                            key={option.value}
                            variant={size === option.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSize(option.value as any)}
                            className="justify-start"
                          >
                            <span className="mr-2">{option.icon}</span>
                            {option.label}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imágenes de Referencia (máx. 4)</Label>
                      <div className="text-xs text-gray-600 bg-blue-50 p-2 rounded">
                        💡 Las imágenes se analizarán con IA para extraer estilo, colores, tipografía y layout
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={(e) => handleReferenceFiles(e.target.files)}
                        className="hidden"
                      />
                      <Button
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Subir Imágenes
                      </Button>
                    </div>

                    {referenceImages.length > 0 && (
                      <div className="space-y-2">
                        <Label className="text-xs text-gray-600">Imágenes seleccionadas:</Label>
                        <div className="grid grid-cols-2 gap-2">
                          {referenceImages.map((file, index) => (
                            <div key={index} className="relative">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Referencia ${index + 1}`}
                                className="w-full h-16 object-cover rounded border"
                              />
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => setReferenceImages(prev => prev.filter((_, i) => i !== index))}
                                className="absolute -top-1 -right-1 h-5 w-5 p-0"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <Button
                      onClick={handleGenerateWithReferences}
                      disabled={isGenerating || !referencePrompt.trim() || referenceImages.length === 0}
                      className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Analizando referencias...
                        </>
                      ) : (
                        <>
                          <ImageIcon className="h-4 w-4 mr-2" />
                          Generar con Referencias
                        </>
                      )}
                    </Button>

                    {referenceImages.length === 0 && (
                      <div className="text-xs text-gray-500 text-center">
                        Sube al menos una imagen de referencia para continuar
                      </div>
                    )}
                  </TabsContent>

                  {/* Tab: Editar */}
                  <TabsContent value="edit" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Cargar Imagen para Editar</Label>
                      <input
                        ref={imageInputRef}
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleLoadImageForEdit(file);
                        }}
                        className="hidden"
                      />
                      <Button
                        variant="outline"
                        onClick={() => imageInputRef.current?.click()}
                        className="w-full"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Subir Imagen
                      </Button>
                    </div>

                    {editingImage && (
                      <>
                        <div className="space-y-3">
                          <Label className="text-sm font-medium">Editor de Máscara</Label>
                          <div className="relative border rounded-lg overflow-hidden bg-gray-100">
                            <div className="relative w-full" style={{ minHeight: '200px' }}>
                              {/* Canvas de imagen original */}
                              <canvas
                                ref={canvasRef}
                                className="block w-full h-auto max-w-full"
                                style={{ display: 'block' }}
                              />
                              {/* Canvas de máscara superpuesto */}
                              <canvas
                                ref={maskCanvasRef}
                                className="absolute top-0 left-0 w-full h-full cursor-crosshair"
                                style={{
                                  opacity: showMask ? 0.7 : 0.3,
                                  mixBlendMode: showMask ? 'normal' : 'multiply'
                                }}
                                onMouseDown={startDrawing}
                                onMouseMove={draw}
                                onMouseUp={stopDrawing}
                                onMouseLeave={stopDrawing}
                              />
                            </div>
                          </div>

                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Label className="text-xs">Pincel:</Label>
                              <Slider
                                value={[brushSize]}
                                onValueChange={(value) => setBrushSize(value[0])}
                                max={50}
                                min={5}
                                step={5}
                                className="w-20"
                              />
                              <span className="text-xs text-gray-500">{brushSize}px</span>
                            </div>

                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setShowMask(!showMask)}
                            >
                              {showMask ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                            </Button>

                            <Button
                              size="sm"
                              variant="outline"
                              onClick={clearMask}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <Label className="text-sm font-medium">¿Qué quieres en el área marcada?</Label>
                          <Textarea
                            placeholder="Describe qué quieres cambiar en las áreas que pintaste..."
                            value={maskPrompt}
                            onChange={(e) => setMaskPrompt(e.target.value)}
                            className="min-h-[80px] resize-none"
                          />
                        </div>

                        <Button
                          onClick={handleEditWithMask}
                          disabled={isGenerating || !maskPrompt.trim()}
                          className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                        >
                          {isGenerating ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Editando...
                            </>
                          ) : (
                            <>
                              <Edit3 className="h-4 w-4 mr-2" />
                              Aplicar Cambios
                            </>
                          )}
                        </Button>
                      </>
                    )}

                    {!editingImage && (
                      <div className="text-center py-8 text-gray-500">
                        <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">Sube una imagen para comenzar a editarla</p>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Panel de Visualización */}
          <div className="lg:col-span-2">
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Resultado
                  </CardTitle>
                  {currentInfographic && (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleToggleFavorite}
                        className={currentInfographicSaved ? "text-red-500 border-red-200 bg-red-50" : ""}
                      >
                        <Heart className={`h-4 w-4 mr-1 ${currentInfographicSaved ? "fill-current" : ""}`} />
                        {currentInfographicSaved ? "Guardado" : "Guardar"}
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleDownload}>
                        <Download className="h-4 w-4 mr-1" />
                        Descargar
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleShare}>
                        <Share2 className="h-4 w-4 mr-1" />
                        Compartir
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>

                {/* Main Result */}
                {currentInfographic ? (
                  <div className="space-y-4">
                    <div className="relative group">
                      <img
                        src={currentInfographic.image_url}
                        alt="Infografía generada"
                        className="w-full rounded-lg shadow-lg"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg" />
                    </div>

                    {/* Metadata */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          <Type className="h-3 w-3 mr-1" />
                          Prompt Original
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                        {currentInfographic.prompt}
                      </p>

                      {currentInfographic.revised_prompt && (
                        <>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">
                              <Sparkles className="h-3 w-3 mr-1" />
                              Prompt Mejorado por IA
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 bg-blue-50 p-3 rounded">
                            {currentInfographic.revised_prompt}
                          </p>
                        </>
                      )}

                      {currentInfographic.metadata && (
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="secondary">
                            Modelo: {currentInfographic.metadata.model}
                          </Badge>
                          {currentInfographic.metadata.size && (
                            <Badge variant="secondary">
                              Tamaño: {currentInfographic.metadata.size}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                    <BarChart3 className="h-16 w-16 mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No hay infografía generada</h3>
                    <p className="text-sm text-center max-w-md">
                      Usa el panel de control para generar tu primera infografía con IA
                    </p>
                  </div>
                )}


              </CardContent>
            </Card>
          </div>
        </div>
          </TabsContent>

          <TabsContent value="saved">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-primary" />
                  Infografías Guardadas
                  <Badge variant="secondary">{savedInfographics.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {savedInfographics.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {savedInfographics.map((savedInfographic) => (
                      <motion.div
                        key={savedInfographic.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="bg-white border-2 border-gray-200 rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                      >
                        {/* Imagen */}
                        <div className="relative aspect-square">
                          <img
                            src={savedInfographic.image_url}
                            alt="Infografía guardada"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-2 right-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const filteredInfographics = savedInfographics.filter(inf => inf.id !== savedInfographic.id);
                                setSavedInfographics(filteredInfographics);

                                toast({
                                  title: "💔 Eliminada",
                                  description: "Infografía eliminada de favoritos.",
                                });
                              }}
                              className="bg-white/90 hover:bg-white"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        {/* Información */}
                        <div className="p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="secondary" className="text-xs">
                              {savedInfographic.type.toUpperCase()}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(savedInfographic.timestamp).toLocaleDateString()}
                            </span>
                          </div>

                          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                            {savedInfographic.prompt}
                          </p>

                          {/* Botones de acción */}
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                // Cargar la infografía en la vista principal
                                setCurrentInfographic({
                                  id: savedInfographic.id,
                                  image_url: savedInfographic.image_url,
                                  prompt: savedInfographic.prompt,
                                  revised_prompt: savedInfographic.revised_prompt,
                                  metadata: savedInfographic.metadata,
                                  timestamp: savedInfographic.timestamp,
                                });

                                // Cambiar a la pestaña "Última Generación"
                                setMainTab("latest");

                                toast({
                                  title: "🖼️ Infografía cargada",
                                  description: "Infografía cargada en la vista principal.",
                                });
                              }}
                              className="flex-1"
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Ver
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const link = document.createElement("a");
                                link.href = savedInfographic.image_url;
                                link.download = `infografia_${savedInfographic.timestamp}.png`;
                                link.click();
                              }}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-600 mb-2">
                      No hay infografías guardadas
                    </h3>
                    <p className="text-gray-500">
                      Las infografías que guardes aparecerán aquí
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default InfographicCreatorPage;