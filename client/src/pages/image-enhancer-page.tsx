/**
 * Página standalone para el Mejorador de Imagen
 * Permite mejorar la resolución y calidad de imágenes usando Stability AI
 */
import React, { useCallback, useState } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Download,
  ImagePlus,
  Sparkles,
  Upload,
  Heart,
  Copy,
  Zap,
  ArrowUpRight,
  Settings,
  Crown,
  Cpu,
  Info,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Interfaces para los modos de upscale
interface UpscaleModeInfo {
  id: string;
  name: string;
  description: string;
  credits: number;
  max_resolution: string;
  requires_prompt: boolean;
  processing_time: string;
}

interface StylePresetInfo {
  id: string;
  name: string;
}

// Función para traducir prompts al inglés (copiada de ai-image-editor)
const translatePromptToEnglish = (spanishPrompt: string): string => {
  const translations: Record<string, string> = {
    // Términos comunes
    'persona': 'person',
    'personas': 'people',
    'hombre': 'man',
    'mujer': 'woman',
    'niño': 'child',
    'niña': 'girl',
    'retrato': 'portrait',
    'cara': 'face',
    'rostro': 'face',
    'sonrisa': 'smile',
    'ojos': 'eyes',
    'cabello': 'hair',
    'pelo': 'hair',

    // Paisajes y lugares
    'paisaje': 'landscape',
    'montaña': 'mountain',
    'montañas': 'mountains',
    'mar': 'sea',
    'océano': 'ocean',
    'playa': 'beach',
    'bosque': 'forest',
    'árbol': 'tree',
    'árboles': 'trees',
    'cielo': 'sky',
    'nubes': 'clouds',
    'sol': 'sun',
    'luna': 'moon',
    'estrella': 'star',
    'estrellas': 'stars',
    'ciudad': 'city',
    'edificio': 'building',
    'edificios': 'buildings',
    'casa': 'house',
    'calle': 'street',
    'carretera': 'road',
    'camino': 'path',

    // Animales
    'perro': 'dog',
    'gato': 'cat',
    'pájaro': 'bird',
    'caballo': 'horse',
    'león': 'lion',
    'tigre': 'tiger',
    'elefante': 'elephant',
    'oso': 'bear',
    'lobo': 'wolf',
    'águila': 'eagle',
    'pez': 'fish',
    'mariposa': 'butterfly',

    // Objetos
    'coche': 'car',
    'auto': 'car',
    'automóvil': 'car',
    'bicicleta': 'bicycle',
    'moto': 'motorcycle',
    'avión': 'airplane',
    'barco': 'boat',
    'tren': 'train',
    'mesa': 'table',
    'silla': 'chair',
    'libro': 'book',
    'teléfono': 'phone',
    'computadora': 'computer',
    'ordenador': 'computer',
    'cámara': 'camera',
    'reloj': 'watch',
    'flor': 'flower',
    'flores': 'flowers',

    // Colores
    'rojo': 'red',
    'azul': 'blue',
    'verde': 'green',
    'amarillo': 'yellow',
    'negro': 'black',
    'blanco': 'white',
    'gris': 'gray',
    'rosa': 'pink',
    'morado': 'purple',
    'naranja': 'orange',
    'marrón': 'brown',
    'dorado': 'golden',
    'plateado': 'silver',

    // Adjetivos
    'grande': 'big',
    'pequeño': 'small',
    'alto': 'tall',
    'bajo': 'short',
    'largo': 'long',
    'corto': 'short',
    'nuevo': 'new',
    'viejo': 'old',
    'joven': 'young',
    'hermoso': 'beautiful',
    'bonito': 'beautiful',
    'feo': 'ugly',
    'bueno': 'good',
    'malo': 'bad',
    'rápido': 'fast',
    'lento': 'slow',
    'fuerte': 'strong',
    'débil': 'weak',
    'caliente': 'hot',
    'frío': 'cold',
    'brillante': 'bright',
    'oscuro': 'dark',
    'claro': 'light',
    'limpio': 'clean',
    'sucio': 'dirty',
    'moderno': 'modern',
    'antiguo': 'ancient',
    'natural': 'natural',
    'artificial': 'artificial',

    // Calidad y estilo
    'alta calidad': 'high quality',
    'buena calidad': 'good quality',
    'profesional': 'professional',
    'detallado': 'detailed',
    'realista': 'realistic',
    'artístico': 'artistic',
    'elegante': 'elegant',
    'simple': 'simple',
    'complejo': 'complex',
    'minimalista': 'minimalist',
    'colorido': 'colorful',
    'vibrante': 'vibrant',
    'suave': 'soft',
    'nítido': 'sharp',
    'borroso': 'blurry',
    'enfocado': 'focused',
    'desenfocado': 'unfocused',
  };

  let translatedPrompt = spanishPrompt.toLowerCase();

  // Aplicar traducciones
  Object.entries(translations).forEach(([spanish, english]) => {
    const regex = new RegExp(`\\b${spanish}\\b`, 'gi');
    translatedPrompt = translatedPrompt.replace(regex, english);
  });

  return translatedPrompt;
};

// Interfaz para imágenes guardadas
interface SavedImage {
  id: string;
  originalUrl: string;
  processedUrl: string;
  originalFilename: string;
  outputFormat: string;
  timestamp: number;
  isFavorite: boolean;
  mode: string;
}

// Custom hook para localStorage
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);

      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
        window.dispatchEvent(new CustomEvent('localStorage', {
          detail: { key, newValue: valueToStore }
        }));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

// Funciones para manejar imágenes guardadas
const SAVED_IMAGES_KEY = 'emma_saved_enhanced_images';

const createSavedImage = (imageData: Omit<SavedImage, 'id' | 'timestamp' | 'isFavorite'>): SavedImage => {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 1000000).toString();

  return {
    ...imageData,
    id: `enhanced_img_${timestamp}_${randomPart}`,
    timestamp: timestamp,
    isFavorite: true,
  };
};

const isImageSaved = (imageUrl: string, savedImages: SavedImage[]): boolean => {
  return savedImages.some(img => img.processedUrl === imageUrl);
};

export default function ImageEnhancerPage() {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();

  const navigate = (path: string) => {
    setLocation(path);
  };

  // Estados principales
  const [originalImage, setOriginalImage] = useState<File | null>(null);
  const [originalImagePreview, setOriginalImagePreview] = useState<string | null>(null);
  const [resultImage, setResultImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [originalName, setOriginalName] = useState<string>("");
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("");

  // Estados de configuración
  const [mode, setMode] = useState<"fast" | "conservative" | "creative">("fast");
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [seed, setSeed] = useState<number | undefined>(undefined);
  const [creativity, setCreativity] = useState([0.3]);
  const [stylePreset, setStylePreset] = useState<string>("");
  const [outputFormat, setOutputFormat] = useState<"jpeg" | "png" | "webp">("webp");

  // Debug: Log cuando cambia el modo
  React.useEffect(() => {
    console.log("Modo cambiado a:", mode);
  }, [mode]);

  // Estados para la funcionalidad de guardados
  const [savedImages, setSavedImages] = useLocalStorage<SavedImage[]>(SAVED_IMAGES_KEY, []);
  const [currentImageSaved, setCurrentImageSaved] = useState(false);
  const [activeTab, setActiveTab] = useState("latest");

  // Datos estáticos de modos (para evitar problemas de carga)
  const modes: UpscaleModeInfo[] = [
    {
      id: "fast",
      name: "Rápido",
      description: "Mejora 4x la resolución en ~1 segundo",
      credits: 1,
      max_resolution: "4x original",
      requires_prompt: false,
      processing_time: "~1 segundo"
    },
    {
      id: "conservative",
      name: "Conservador",
      description: "Mejora hasta 4K preservando aspectos originales",
      credits: 25,
      max_resolution: "4K",
      requires_prompt: true,
      processing_time: "~30-60 segundos"
    },
    {
      id: "creative",
      name: "Creativo",
      description: "Mejora hasta 4K reimaginando la imagen",
      credits: 25,
      max_resolution: "4K",
      requires_prompt: true,
      processing_time: "~2-5 minutos"
    }
  ];

  const stylePresets: StylePresetInfo[] = [
    { id: "photographic", name: "Fotográfico" },
    { id: "digital-art", name: "Arte Digital" },
    { id: "comic-book", name: "Cómic" },
    { id: "fantasy-art", name: "Arte Fantástico" },
    { id: "line-art", name: "Arte Lineal" },
    { id: "analog-film", name: "Película Analógica" },
    { id: "neon-punk", name: "Neon Punk" },
    { id: "isometric", name: "Isométrico" },
    { id: "low-poly", name: "Low Poly" },
    { id: "origami", name: "Origami" },
    { id: "modeling-compound", name: "Plastilina" },
    { id: "cinematic", name: "Cinematográfico" },
    { id: "3d-model", name: "Modelo 3D" },
    { id: "pixel-art", name: "Pixel Art" }
  ];

  // Información del modo actual
  const currentModeInfo = modes.find(m => m.id === mode);

  // Generar seed aleatorio
  const generateRandomSeed = () => {
    const randomSeed = Math.floor(Math.random() * 4294967294);
    setSeed(randomSeed);
    toast({
      title: "🎲 Seed aleatorio generado",
      description: `Nuevo seed: ${randomSeed}`,
    });
  };

  // Manejar la selección de imagen
  const handleImageSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validar que sea una imagen
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Formato no soportado",
          description: "Por favor, selecciona un archivo de imagen válido.",
          variant: "destructive",
        });
        return;
      }

      // Guardar el nombre original
      setOriginalName(file.name);

      // Crear URL para previsualización
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setOriginalImagePreview(result);
        setOriginalImage(file);
        setResultImage(null); // Limpiar resultado anterior
      };
      reader.readAsDataURL(file);

      toast({
        title: "Imagen cargada",
        description: "Ahora puedes mejorar su calidad con IA.",
      });
    },
    [toast],
  );

  // Función para polling de resultados creativos (copiada de ai-image-editor)
  const pollCreativeUpscaleResult = async (generationId: string) => {
    const maxAttempts = 30; // 5 minutos máximo
    const pollInterval = 10000; // 10 segundos

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        await new Promise(resolve => setTimeout(resolve, pollInterval));

        const response = await fetch(`/api/v1/images/enhance-image/creative/result/${generationId}`);

        if (response.status === 202) {
          // Aún procesando
          const progress = Math.min(20 + (attempt / maxAttempts) * 70, 90);
          setProgress(progress);
          setProgressMessage(`Procesando con IA... (${attempt + 1}/${maxAttempts})`);
          continue;
        }

        if (!response.ok) {
          throw new Error(`Error ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success && result.url) {
          setResultImage(result.url);
          setProgress(100);
          setProgressMessage("¡Imagen mejorada exitosamente!");
          toast({
            title: "🎉 ¡Calidad mejorada!",
            description: "Imagen procesada exitosamente con modo creativo",
          });
          return;
        } else {
          throw new Error(result.error || 'Error en el procesamiento creativo');
        }
      } catch (error) {
        console.error(`Error en intento ${attempt + 1}:`, error);
        if (attempt === maxAttempts - 1) {
          throw new Error('Tiempo de espera agotado. Intenta de nuevo.');
        }
      }
    }
  };

  // Procesar la imagen (usando el mismo patrón que ai-image-editor)
  const handleProcessImage = useCallback(async () => {
    if (!originalImage) {
      toast({
        title: "Faltan datos",
        description: "Por favor, selecciona una imagen primero.",
        variant: "destructive",
      });
      return;
    }

    // Validar prompt para modos que lo requieren
    if ((mode === "conservative" || mode === "creative") && !prompt.trim()) {
      toast({
        title: "❌ Prompt requerido",
        description: `El modo ${mode === "conservative" ? "Conservador" : "Creativo"} requiere una descripción`,
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setProgressMessage("Iniciando mejora de calidad...");
    setResultImage(null);

    try {
      // Crear FormData (igual que en ai-image-editor)
      const formData = new FormData();
      formData.append('image', originalImage);
      formData.append('upscale_type', mode);
      formData.append('output_format', outputFormat);

      if (mode !== "fast") {
        // Traducir el prompt al inglés para Stability AI
        const originalPrompt = prompt.trim() || 'high quality image';
        console.log(`🔄 Traduciendo prompt: "${originalPrompt}"`);
        const translatedPrompt = translatePromptToEnglish(originalPrompt);
        console.log(`✅ Prompt traducido: "${translatedPrompt}"`);

        formData.append('prompt', translatedPrompt);
      }

      formData.append('creativity', creativity[0].toString());

      if (mode === "creative" && stylePreset) {
        formData.append('style_preset', stylePreset);
      }

      setProgress(25);
      setProgressMessage("Enviando imagen a la IA...");

      // Llamar a la API (igual que en ai-image-editor)
      const response = await fetch('/api/v1/images/enhance-image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();

      if (result.success) {
        if (mode === "creative" && result.metadata?.generation_id) {
          // Para creative upscale, iniciar polling
          setProgress(50);
          setProgressMessage("Procesando con IA creativa...");
          await pollCreativeUpscaleResult(result.metadata.generation_id);
        } else if (result.url) {
          // Para fast y conservative, resultado inmediato
          setResultImage(result.url);
          toast({
            title: "🎉 ¡Calidad mejorada!",
            description: `Imagen procesada exitosamente con modo ${currentModeInfo?.name || mode}`,
          });
        }
      } else {
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error("Error en upscale:", error);
      toast({
        title: "❌ Error al mejorar",
        description: error instanceof Error ? error.message : "Error desconocido",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProgress(0);
      setProgressMessage("");
    }
  }, [originalImage, mode, prompt, negativePrompt, seed, outputFormat, creativity, stylePreset, currentModeInfo, toast]);

  // Manejar descarga
  const handleDownload = useCallback(() => {
    if (!resultImage) return;

    const link = document.createElement("a");
    link.href = resultImage;
    link.download = `${originalName.replace(/\.[^/.]+$/, "")}_enhanced.${outputFormat}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "📥 Descarga iniciada",
      description: "La imagen mejorada se está descargando",
    });
  }, [resultImage, originalName, outputFormat, toast]);

  // Manejar copia al portapapeles
  const handleCopyToClipboard = useCallback(async () => {
    if (!resultImage) return;

    try {
      const response = await fetch(resultImage);
      const blob = await response.blob();

      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);

      toast({
        title: "📋 ¡Copiada!",
        description: "Imagen copiada al portapapeles exitosamente",
      });
    } catch (error) {
      console.error("Error copiando al portapapeles:", error);
      toast({
        title: "❌ Error al copiar",
        description: "No se pudo copiar la imagen al portapapeles",
        variant: "destructive",
      });
    }
  }, [resultImage, toast]);

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!resultImage || !originalImagePreview) return;

    try {
      if (currentImageSaved) {
        // Quitar de favoritos
        const savedImage = savedImages.find(img => img.processedUrl === resultImage);
        if (savedImage) {
          const filteredImages = savedImages.filter(img => img.id !== savedImage.id);
          setSavedImages(filteredImages);
          setCurrentImageSaved(false);

          toast({
            title: "💔 Eliminada de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const imageData = {
          originalUrl: originalImagePreview,
          processedUrl: resultImage,
          originalFilename: originalName || "imagen",
          outputFormat,
          mode: currentModeInfo?.name || mode,
        };

        const newImage = createSavedImage(imageData);
        const updatedImages = [newImage, ...savedImages].slice(0, 50); // Limitar a 50

        setSavedImages(updatedImages);
        setCurrentImageSaved(true);

        toast({
          title: "❤️ ¡Guardada en favoritos!",
          description: "Imagen guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar la imagen. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [resultImage, originalImagePreview, originalName, outputFormat, mode, currentModeInfo, currentImageSaved, savedImages, setSavedImages, toast]);

  // Verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (resultImage) {
      setCurrentImageSaved(isImageSaved(resultImage, savedImages));
    }
  }, [resultImage, savedImages]);

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-background">
        {/* Header moderno */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white relative overflow-hidden"
        >
          <div className="container mx-auto py-12 px-4 relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  onClick={() => navigate("/visual-tools")}
                  className="text-white hover:bg-white/20 transition-all duration-300"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Volver
                </Button>
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-white/20 rounded-lg">
                    <ArrowUpRight className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold flex items-center gap-2">
                      Mejorador de Imagen
                      <Badge className="bg-yellow-400 text-black font-bold">
                        IA
                      </Badge>
                    </h1>
                    <p className="text-white/80 text-sm">
                      Mejora la resolución y calidad con IA avanzada
                    </p>
                  </div>
                </div>
              </div>

              <div className="hidden md:flex items-center space-x-2">
                <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                  <Zap className="h-3 w-3 mr-1" />
                  Rápido
                </Badge>
                <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Hasta 4K
                </Badge>
                <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                  <Crown className="h-3 w-3 mr-1" />
                  Profesional
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="container mx-auto py-8 px-4">
          {/* Descripción */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-3xl mx-auto mb-12 text-center"
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full mb-4">
              <Sparkles className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium text-primary">
                Tecnología de IA Avanzada
              </span>
            </div>
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Mejora la calidad de tus imágenes
            </h2>
            <p className="text-muted-foreground text-lg">
              Nuestra IA mejora automáticamente la resolución y calidad de cualquier imagen
              hasta 4K con resultados profesionales.
            </p>
          </motion.div>

          {/* Tabs para organizar contenido */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="max-w-6xl mx-auto">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="latest">Última Generación</TabsTrigger>
              <TabsTrigger value="saved">
                Guardados ({savedImages.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="latest" className="mt-8">
              {/* Grid principal */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
                {/* Panel de entrada */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Upload className="h-5 w-5 text-primary" />
                        Imagen Original
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {!originalImage ? (
                        <div className="flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-12 text-center min-h-[300px]">
                          <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                            <ImagePlus className="h-8 w-8 text-primary" />
                          </div>
                          <h3 className="text-lg font-semibold mb-2">
                            Sube tu imagen
                          </h3>
                          <p className="text-muted-foreground mb-6 max-w-sm">
                            Selecciona una imagen para mejorar su calidad automáticamente
                          </p>
                          <Button asChild>
                            <label className="cursor-pointer">
                              <Upload className="h-4 w-4 mr-2" />
                              Seleccionar imagen
                              <input
                                type="file"
                                accept="image/*"
                                className="hidden"
                                onChange={handleImageSelect}
                              />
                            </label>
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="relative rounded-lg overflow-hidden border">
                            <img
                              src={originalImagePreview!}
                              alt="Imagen original"
                              className="w-full object-contain max-h-80"
                            />
                            <div className="absolute top-2 right-2">
                              <Button size="sm" variant="secondary" asChild>
                                <label className="cursor-pointer">
                                  <ImagePlus className="h-4 w-4 mr-1" />
                                  Cambiar
                                  <input
                                    type="file"
                                    accept="image/*"
                                    className="hidden"
                                    onChange={handleImageSelect}
                                  />
                                </label>
                              </Button>
                            </div>
                          </div>

                          <div className="text-sm text-muted-foreground">
                            <p><strong>Archivo:</strong> {originalName}</p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Panel de resultado */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Sparkles className="h-5 w-5 text-primary" />
                        Imagen Mejorada
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {resultImage ? (
                        <div className="space-y-4">
                          <div className="relative rounded-lg overflow-hidden border">
                            <img
                              src={resultImage}
                              alt="Imagen mejorada"
                              className="w-full object-contain max-h-80"
                            />
                          </div>

                          {/* Acciones */}
                          <div className="flex flex-wrap gap-2">
                            <Button onClick={handleDownload} variant="outline">
                              <Download className="w-4 h-4 mr-2" />
                              Descargar
                            </Button>
                            <Button onClick={handleCopyToClipboard} variant="outline">
                              <Copy className="w-4 h-4 mr-2" />
                              Copiar
                            </Button>
                            <Button
                              onClick={handleToggleFavorite}
                              variant="outline"
                              className={currentImageSaved ? "bg-red-50 border-red-200 text-red-700 hover:bg-red-100" : ""}
                            >
                              <Heart className={`w-4 h-4 mr-2 ${currentImageSaved ? "fill-current" : ""}`} />
                              {currentImageSaved ? "Quitar" : "Guardar"}
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <Sparkles className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-muted-foreground">
                            La imagen mejorada aparecerá aquí
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              {/* Panel de configuración */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="mt-8"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      Configuración de Mejora
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {/* Selector de modo */}
                      <div className="space-y-2">
                        <Label>Modo de Mejora</Label>
                        <Select value={mode} onValueChange={(value: any) => setMode(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {modes.map((modeInfo) => (
                              <SelectItem key={modeInfo.id} value={modeInfo.id}>
                                <div className="flex items-center gap-2">
                                  <span>{modeInfo.name}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {modeInfo.credits} créditos
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {currentModeInfo && (
                          <div className="text-xs text-muted-foreground space-y-1">
                            <p>{currentModeInfo.description}</p>
                            <p><strong>Resolución máxima:</strong> {currentModeInfo.max_resolution}</p>
                            <p><strong>Tiempo estimado:</strong> {currentModeInfo.processing_time}</p>
                          </div>
                        )}
                      </div>

                      {/* Formato de salida */}
                      <div className="space-y-2">
                        <Label>Formato de Salida</Label>
                        <Select value={outputFormat} onValueChange={(value: any) => setOutputFormat(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="webp">WebP (recomendado)</SelectItem>
                            <SelectItem value="png">PNG (sin pérdida)</SelectItem>
                            <SelectItem value="jpeg">JPEG (menor tamaño)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Seed (opcional) */}
                      <div className="space-y-2">
                        <Label>Seed (opcional)</Label>
                        <div className="flex gap-2">
                          <Input
                            type="number"
                            placeholder="Aleatorio"
                            value={seed || ""}
                            onChange={(e) => setSeed(e.target.value ? parseInt(e.target.value) : undefined)}
                            min={0}
                            max={4294967294}
                            className="flex-1"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={generateRandomSeed}
                            className="px-3"
                          >
                            🎲 Random
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Controla la reproducibilidad del resultado
                        </p>
                      </div>
                    </div>

                    {/* Prompt (para modos conservative y creative) */}
                    {(mode === "conservative" || mode === "creative") && (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label>
                            Descripción de la imagen {mode === "conservative" || mode === "creative" ? "*" : ""}
                          </Label>
                          <Textarea
                            placeholder="Describe la imagen para obtener mejores resultados (ej: 'retrato de una persona', 'paisaje natural', 'edificio moderno')"
                            value={prompt}
                            onChange={(e) => setPrompt(e.target.value)}
                            rows={3}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Prompt Negativo (opcional)</Label>
                          <Textarea
                            placeholder="Qué NO quieres ver en la imagen (ej: 'borroso, pixelado, artefactos')"
                            value={negativePrompt}
                            onChange={(e) => setNegativePrompt(e.target.value)}
                            rows={2}
                          />
                        </div>
                      </div>
                    )}

                    {/* Creatividad (para modo creative) */}
                    {mode === "creative" && (
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label>Nivel de Creatividad: {creativity?.[0] || 0.3}</Label>
                          <Slider
                            value={creativity || [0.3]}
                            onValueChange={(value) => setCreativity(value || [0.3])}
                            min={0.1}
                            max={0.5}
                            step={0.1}
                            className="w-full"
                          />
                          <div className="text-xs text-muted-foreground">
                            Valores más altos agregan más detalles durante la mejora
                          </div>
                        </div>

                        {/* Estilo (para modo creative) */}
                        {stylePresets.length > 0 && (
                          <div className="space-y-2">
                            <Label>Estilo (opcional)</Label>
                            <Select value={stylePreset || "none"} onValueChange={(value) => setStylePreset(value === "none" ? "" : value)}>
                              <SelectTrigger>
                                <SelectValue placeholder="Seleccionar estilo" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Sin estilo específico</SelectItem>
                                {stylePresets.map((preset) => (
                                  <SelectItem key={preset.id} value={preset.id}>
                                    {preset.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Botón de procesamiento */}
                    <div className="flex flex-col space-y-4">
                      <Button
                        onClick={handleProcessImage}
                        disabled={isProcessing || !originalImage}
                        className="w-full"
                        size="lg"
                      >
                        {isProcessing ? (
                          <>
                            <Cpu className="w-4 h-4 mr-2 animate-spin" />
                            Mejorando Calidad...
                          </>
                        ) : (
                          <>
                            <Sparkles className="w-4 h-4 mr-2" />
                            Mejorar Calidad
                          </>
                        )}
                      </Button>

                      {/* Barra de progreso */}
                      {isProcessing && (
                        <div className="space-y-2">
                          <Progress value={progress} className="w-full" />
                          <p className="text-sm text-muted-foreground text-center">
                            {progressMessage}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="saved" className="mt-8">
              {/* Galería de imágenes guardadas */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {savedImages.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {savedImages.map((savedImage) => (
                      <Card key={savedImage.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                        <div className="aspect-square relative">
                          <img
                            src={savedImage.processedUrl}
                            alt={`Imagen mejorada - ${savedImage.originalFilename}`}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-2 right-2">
                            <Badge variant="secondary" className="bg-black/50 text-white">
                              {savedImage.mode}
                            </Badge>
                          </div>
                        </div>
                        <CardContent className="p-4">
                          <div className="space-y-2">
                            <h3 className="font-medium text-sm truncate">
                              {savedImage.originalFilename}
                            </h3>
                            <p className="text-xs text-muted-foreground">
                              {new Date(savedImage.timestamp).toLocaleDateString('es-ES', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex-1"
                                onClick={() => {
                                  const link = document.createElement("a");
                                  link.href = savedImage.processedUrl;
                                  link.download = `${savedImage.originalFilename.replace(/\.[^/.]+$/, "")}_enhanced.${savedImage.outputFormat}`;
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }}
                              >
                                <Download className="w-3 h-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const filteredImages = savedImages.filter(img => img.id !== savedImage.id);
                                  setSavedImages(filteredImages);
                                  toast({
                                    title: "Imagen eliminada",
                                    description: "La imagen ha sido eliminada de tus guardados.",
                                  });
                                }}
                              >
                                <Heart className="w-3 h-3 fill-current text-red-500" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      No tienes imágenes guardadas
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Las imágenes que mejores y guardes aparecerán aquí
                    </p>
                    <Button onClick={() => setActiveTab("latest")}>
                      Mejorar una imagen
                    </Button>
                  </div>
                )}
              </motion.div>
            </TabsContent>
          </Tabs>

          {/* Panel de información adicional */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mt-12 max-w-4xl mx-auto"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Información sobre los Modos de Mejora
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Zap className="h-5 w-5 text-blue-600" />
                      <h3 className="font-semibold">Modo Rápido</h3>
                    </div>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Mejora 4x la resolución</li>
                      <li>• Procesamiento en ~1 segundo</li>
                      <li>• Ideal para redes sociales</li>
                      <li>• Solo 1 crédito por imagen</li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Crown className="h-5 w-5 text-purple-600" />
                      <h3 className="font-semibold">Modo Conservador</h3>
                    </div>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Mejora hasta 4K</li>
                      <li>• Preserva aspectos originales</li>
                      <li>• Requiere descripción</li>
                      <li>• 25 créditos por imagen</li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-pink-600" />
                      <h3 className="font-semibold">Modo Creativo</h3>
                    </div>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Mejora hasta 4K</li>
                      <li>• Reimagina la imagen</li>
                      <li>• Ideal para imágenes degradadas</li>
                      <li>• 25 créditos por imagen</li>
                    </ul>
                  </div>
                </div>

                <div className="border-t pt-6">
                  <h4 className="font-semibold mb-3">Consejos para mejores resultados:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <ul className="space-y-1">
                      <li>• Usa modo Rápido para mejoras simples y rápidas</li>
                      <li>• Usa modo Conservador para preservar detalles importantes</li>
                      <li>• Usa modo Creativo para imágenes muy degradadas o pixeladas</li>
                    </ul>
                    <ul className="space-y-1">
                      <li>• Describe bien la imagen para mejores resultados</li>
                      <li>• El prompt negativo ayuda a evitar elementos no deseados</li>
                      <li>• Experimenta con diferentes niveles de creatividad</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </TooltipProvider>
  );
}
