/* Emma AgenticSeek Styles */
.emma-agenticseek-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.header {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.emma-branding {
  display: flex;
  align-items: center;
  gap: 16px;
}

.emma-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.emma-info h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.emma-info p {
  font-size: 0.9rem;
  color: #64748b;
  margin: 2px 0 0 0;
  font-weight: 400;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.05);
  padding: 8px 16px;
  border-radius: 20px;
  color: #475569;
  font-size: 0.85rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-dot.online {
  background-color: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.status-dot.offline {
  background-color: #ef4444;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.main {
  flex: 1;
  padding: 16px;
  width: 100%;
}

.mode-selector {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  justify-content: center;
}

.mode-btn {
  padding: 10px 20px;
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.mode-btn.active {
  background-color: #8b5cf6;
  color: #ffffff;
  border-color: #8b5cf6;
}

.mode-btn:hover:not(.active) {
  background-color: #e2e8f0;
  color: #1e293b;
}

.app-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  height: calc(100vh - 200px);
}

.chat-section,
.computer-section {
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-section h2,
.computer-section h2 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 12px;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 8px;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 12px 8px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 8px;
}

.welcome-message {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  margin-bottom: 20px;
}

.welcome-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid rgba(226, 232, 240, 0.5);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.welcome-content h3 {
  color: #1e293b;
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.welcome-content p {
  color: #475569;
  font-size: 1rem;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.welcome-content ul {
  list-style: none;
  padding: 0;
  margin: 0 0 16px 0;
}

.welcome-content li {
  color: #475569;
  font-size: 0.95rem;
  margin: 8px 0;
  padding-left: 8px;
}

.welcome-cta {
  color: #1e293b !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  margin: 20px 0 0 0 !important;
}

.message {
  max-width: 85%;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 0.95rem;
  line-height: 1.5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-message {
  background-color: #8b5cf6;
  color: #ffffff;
  align-self: flex-end;
  border-top-right-radius: 4px;
}

.agent-message {
  background-color: #f1f5f9;
  color: #1e293b;
  align-self: flex-start;
  border-top-left-radius: 4px;
}

.error-message {
  background-color: #dc3545;
  color: #ffffff;
  align-self: flex-start;
  border-top-left-radius: 4px;
}

.message-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 8px;
}

.agent-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(226, 232, 240, 0.5);
  flex-shrink: 0;
}

.agent-name {
  font-size: 0.85rem;
  color: #475569;
  font-weight: 600;
  margin: 0;
}

.reasoning-toggle {
  background: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 4px;
  color: #8b5cf6;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  align-self: flex-start;
}

.reasoning-toggle:hover {
  background: rgba(139, 92, 246, 0.2);
  border-color: rgba(139, 92, 246, 0.3);
}

.reasoning-content {
  margin-top: 12px;
  padding: 12px;
  background: rgba(139, 92, 246, 0.05);
  border-left: 3px solid rgba(139, 92, 246, 0.3);
  border-radius: 0 4px 4px 0;
  font-size: 0.9em;
  line-height: 1.4;
}

.input-form {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.input-container {
  display: flex;
  flex: 1;
  gap: 8px;
}

.input-form input {
  flex: 1;
  padding: 12px 16px;
  font-size: 0.95rem;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  color: #1e293b;
  border-radius: 8px;
  outline: none;
  transition: all 0.2s ease;
}

.input-form input:focus {
  border-color: #8b5cf6;
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

.input-actions {
  display: flex;
  gap: 8px;
}

.file-upload-btn {
  padding: 12px;
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-upload-btn:hover {
  background-color: #e2e8f0;
}

.input-form button {
  padding: 12px 20px;
  font-size: 0.95rem;
  background-color: #8b5cf6;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.input-form button:hover {
  background-color: #7c3aed;
}

.input-form button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}
