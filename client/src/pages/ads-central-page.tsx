import React, { useState } from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import {
  Target,
  Megaphone,
  Sparkles,
  TrendingUp,
  Users,
  BarChart3,
  Zap,
  ArrowRight,
  Play,
  <PERSON>,
  <PERSON>Pointer,
  DollarSign,
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

// Importar imagen del ad creator
import adCreador from "@/assets/ad-creator.png";

interface AdTool {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  linkTo: string;
  isImplemented: boolean;
  isNew?: boolean;
  isPremium?: boolean;
  previewImage?: string;
  gradient: string;
  category: string;
}

const adTools: AdTool[] = [
  {
    id: "ad-templates",
    title: "Plantillas de Anuncios",
    description: "Biblioteca completa de plantillas profesionales de Figma. Sube, organiza y personaliza tus diseños",
    category: "Plantillas",
    icon: <Sparkles className="w-6 h-6" />,
    linkTo: "/dashboard/ads-central/templates",
    isImplemented: true,
    isNew: true,
    gradient: "from-purple-500 to-blue-600",
  },
  {
    id: "ad-creator",
    title: "Crear Anuncio",
    description: "Diseña anuncios profesionales con calidad de agencia. Product placement perfecto, iluminación profesional y composición comercial",
    category: "Creativos",
    icon: <Megaphone className="w-6 h-6" />,
    linkTo: "/ad-creator",
    isImplemented: true,
    isNew: true,
    previewImage: adCreador,
    gradient: "from-emerald-500 to-teal-600",
  },
  {
    id: "facebook-ads",
    title: "Facebook Ads",
    description: "Crea anuncios optimizados para Facebook e Instagram con formatos específicos y mejores prácticas",
    category: "Plataformas",
    icon: <Users className="w-6 h-6" />,
    linkTo: "/ads-central/facebook",
    isImplemented: false,
    gradient: "from-blue-500 to-blue-600",
  },
  {
    id: "google-ads",
    title: "Google Ads",
    description: "Genera anuncios para Google Ads con copy optimizado y extensiones automáticas",
    category: "Plataformas",
    icon: <Target className="w-6 h-6" />,
    linkTo: "/ads-central/google",
    isImplemented: false,
    gradient: "from-green-500 to-green-600",
  },
  {
    id: "linkedin-ads",
    title: "LinkedIn Ads",
    description: "Anuncios profesionales para LinkedIn con targeting B2B y copy especializado",
    category: "Plataformas",
    icon: <TrendingUp className="w-6 h-6" />,
    linkTo: "/ads-central/linkedin",
    isImplemented: false,
    gradient: "from-blue-600 to-indigo-600",
  },
  {
    id: "youtube-ads",
    title: "YouTube Ads",
    description: "Crea anuncios de video para YouTube con scripts optimizados y thumbnails atractivos",
    category: "Video",
    icon: <Play className="w-6 h-6" />,
    linkTo: "/ads-central/youtube",
    isImplemented: false,
    gradient: "from-red-500 to-red-600",
  },
  {
    id: "ab-testing",
    title: "A/B Testing",
    description: "Genera múltiples variaciones de anuncios para optimizar el rendimiento",
    category: "Optimización",
    icon: <BarChart3 className="w-6 h-6" />,
    linkTo: "/ads-central/ab-testing",
    isImplemented: false,
    gradient: "from-purple-500 to-purple-600",
  },
];

const categories = [
  { id: "all", name: "Todas", icon: <Sparkles className="w-5 h-5" /> },
  { id: "Plantillas", name: "Plantillas", icon: <Sparkles className="w-5 h-5" /> },
  { id: "Creativos", name: "Creativos", icon: <Megaphone className="w-5 h-5" /> },
  { id: "Plataformas", name: "Plataformas", icon: <Target className="w-5 h-5" /> },
  { id: "Video", name: "Video", icon: <Play className="w-5 h-5" /> },
  { id: "Optimización", name: "Optimización", icon: <BarChart3 className="w-5 h-5" /> },
];

function AdsCentralContent() {
  const [, navigate] = useLocation();
  const [selectedCategory, setSelectedCategory] = useState("all");

  const filteredTools = selectedCategory === "all" 
    ? adTools 
    : adTools.filter(tool => tool.category === selectedCategory);

  const stats = [
    { label: "Anuncios Creados", value: "2,847", icon: <Eye className="w-5 h-5" />, color: "text-blue-600" },
    { label: "CTR Promedio", value: "3.2%", icon: <MousePointer className="w-5 h-5" />, color: "text-green-600" },
    { label: "ROI Promedio", value: "285%", icon: <DollarSign className="w-5 h-5" />, color: "text-purple-600" },
    { label: "Conversiones", value: "12,459", icon: <TrendingUp className="w-5 h-5" />, color: "text-orange-600" },
  ];

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 p-8 text-white"
      >
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
              <Target className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold">Ads Central</h1>
          </div>
          <p className="text-xl text-emerald-100 mb-6 max-w-3xl">
            Centro de creación de anuncios con IA especializada en publicidad. 
            Crea campañas profesionales para todas las plataformas principales.
          </p>
          <div className="flex flex-wrap gap-2">
            <Badge className="bg-white/20 text-white border-white/30">
              <Sparkles className="w-3 h-3 mr-1" />
              IA Especializada
            </Badge>
            <Badge className="bg-white/20 text-white border-white/30">
              <Target className="w-3 h-3 mr-1" />
              Multi-plataforma
            </Badge>
            <Badge className="bg-white/20 text-white border-white/30">
              <Zap className="w-3 h-3 mr-1" />
              Optimización Automática
            </Badge>
          </div>
        </div>
        
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full blur-2xl"></div>
      </motion.div>

      {/* Stats Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {stats.map((stat, index) => (
          <Card key={index} className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg bg-gray-100 ${stat.color}`}>
                  {stat.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </motion.div>

      {/* Category Filter */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="flex flex-wrap gap-2"
      >
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? "default" : "outline"}
            onClick={() => setSelectedCategory(category.id)}
            className="flex items-center gap-2"
          >
            {category.icon}
            {category.name}
          </Button>
        ))}
      </motion.div>

      {/* Tools Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {filteredTools.map((tool, index) => (
          <motion.div
            key={tool.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * index }}
            whileHover={{ y: -5 }}
            className="group"
          >
            <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
              {tool.previewImage && (
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={tool.previewImage}
                    alt={tool.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className={`absolute inset-0 bg-gradient-to-t ${tool.gradient} opacity-20`}></div>
                </div>
              )}
              
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${tool.gradient} text-white`}>
                    {tool.icon}
                  </div>
                  <div className="flex gap-1">
                    {tool.isNew && (
                      <Badge variant="secondary" className="text-xs">
                        Nuevo
                      </Badge>
                    )}
                    {tool.isPremium && (
                      <Badge variant="outline" className="text-xs">
                        Premium
                      </Badge>
                    )}
                    {!tool.isImplemented && (
                      <Badge variant="outline" className="text-xs">
                        Próximamente
                      </Badge>
                    )}
                  </div>
                </div>
                <CardTitle className="text-lg">{tool.title}</CardTitle>
                <CardDescription className="text-sm">
                  {tool.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                <Button
                  onClick={() => navigate(tool.linkTo)}
                  disabled={!tool.isImplemented}
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
                  variant={tool.isImplemented ? "default" : "outline"}
                >
                  {tool.isImplemented ? "Usar Herramienta" : "Próximamente"}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}

function AdsCentralPage() {
  return (
    <DashboardLayout pageTitle="Ads Central">
      <AdsCentralContent />
    </DashboardLayout>
  );
}

export default AdsCentralPage;
