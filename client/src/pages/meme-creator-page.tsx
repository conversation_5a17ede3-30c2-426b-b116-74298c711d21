/**
 * Meme Creator Page - Generación y edición de memes con IA
 * Basado en infographic-creator-page.tsx pero adaptado para memes
 */

import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Smile,
  Wand2,
  RefreshCw,
  Download,
  Share2,
  Heart,
  HeartOff,
  Upload,
  X,
  Brush,
  Eraser,
  Sparkles,
  ImageIcon,
  Edit3,
  Palette,
  FileImage,
  Trash2,
  Eye,
  EyeOff,
  RotateCcw,
  Settings,
  Layers,
  PaintBucket,
  Zap,
  Lightbulb,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  generateMeme,
  editWithReferences,
  editWithMask,
  validateImageFile,
  type MemeGenerationOptions,
  type MemeResponse
} from "@/services/meme-service";

// Tipos para el estado de la aplicación
interface GeneratedMeme {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  timestamp: number;
}

interface SavedMeme {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  metadata?: any;
  type: "basic" | "reference" | "mask_edit";
  timestamp: number;
}

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

// Utilidades para memes guardados
const SAVED_MEMES_KEY = "emma-saved-memes";

function createSavedMeme(data: {
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  metadata?: any;
  type: "basic" | "reference" | "mask_edit";
}): SavedMeme {
  return {
    id: `meme-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: Date.now(),
    ...data,
  };
}

function isMemeSaved(imageUrl: string, savedMemes: SavedMeme[]): boolean {
  return savedMemes.some(meme => meme.image_url === imageUrl);
}

export default function MemeCreatorPage() {
  // Estados principales
  const [currentMeme, setCurrentMeme] = useState<GeneratedMeme | null>(null);
  const [prompt, setPrompt] = useState("");
  const [size, setSize] = useState<"1024x1024" | "1536x1024" | "1024x1536">("1024x1024");
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para edición con referencias
  const [referenceImages, setReferenceImages] = useState<File[]>([]);
  const [referencePrompt, setReferencePrompt] = useState("");

  // Estados para edición con máscara
  const [editingImage, setEditingImage] = useState<string | null>(null);
  const [maskPrompt, setMaskPrompt] = useState("");
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(20);
  const [showMask, setShowMask] = useState(true);

  // Estados para favoritos
  const [savedMemes, setSavedMemes] = useLocalStorage<SavedMeme[]>(SAVED_MEMES_KEY, []);
  const [currentMemeSaved, setCurrentMemeSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Prompts de ejemplo para memes
  const examplePrompts = [
    "Meme Cheems en trabajo remoto",
    "Pepe the frog triste viendo a la gente ser feliz",
    "Distracted Boyfriend escuela vs no ir a clase"
  ];

  // Tamaños disponibles según documentación OpenAI
  const sizeOptions = [
    { value: "1024x1024", label: "Cuadrado (1024x1024)", icon: "⬜" },
    { value: "1536x1024", label: "Horizontal (1536x1024)", icon: "▭" },
    { value: "1024x1536", label: "Vertical (1024x1536)", icon: "▯" }
  ];

  // Función para generar meme inicial
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Prompt requerido",
        description: "Por favor, describe el meme que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const options: MemeGenerationOptions = {
        prompt,
        size,
      };

      const result = await generateMeme(options);

      if (result.success && result.image_url) {
        const newMeme: GeneratedMeme = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt,
          revised_prompt: result.revised_prompt,
          response_id: result.response_id,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentMeme(newMeme);

        toast({
          title: "¡Meme generado!",
          description: "Tu meme ha sido creado exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating meme:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar el meme",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para cargar imagen para editar
  const handleLoadImageForEdit = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      setEditingImage(imageUrl);

      // Cargar imagen en el canvas
      setTimeout(() => {
        const canvas = canvasRef.current;
        const maskCanvas = maskCanvasRef.current;
        if (canvas && maskCanvas) {
          const ctx = canvas.getContext('2d');
          const maskCtx = maskCanvas.getContext('2d');
          const img = new Image();

          img.onload = () => {
            // Calcular tamaño apropiado para el canvas (máximo 600px de ancho)
            const maxWidth = 600;
            const maxHeight = 400;
            let { width, height } = img;

            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }

            if (height > maxHeight) {
              width = (width * maxHeight) / height;
              height = maxHeight;
            }

            // Configurar tamaño del canvas
            canvas.width = width;
            canvas.height = height;
            maskCanvas.width = width;
            maskCanvas.height = height;

            // Aplicar tamaño CSS para que se vea correctamente
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            maskCanvas.style.width = `${width}px`;
            maskCanvas.style.height = `${height}px`;

            // Dibujar imagen original escalada
            ctx?.drawImage(img, 0, 0, width, height);

            // Limpiar máscara (fondo negro)
            if (maskCtx) {
              maskCtx.fillStyle = 'black';
              maskCtx.fillRect(0, 0, width, height);
            }
          };

          img.src = imageUrl;
        }
      }, 100);
    };
    reader.readAsDataURL(file);
  };

  // Funciones para dibujar en el canvas
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    draw(e);
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();

    // Calcular la escala entre el canvas real y el canvas mostrado
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    // Ajustar coordenadas según la escala
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = 'white';
    ctx.beginPath();
    ctx.arc(x, y, (brushSize / 2) * scaleX, 0, 2 * Math.PI);
    ctx.fill();
  };

  // Función para limpiar máscara
  const clearMask = () => {
    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  };

  // Función para manejar archivos de referencia
  const handleReferenceFiles = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      const validation = validateImageFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    if (errors.length > 0) {
      toast({
        title: "Archivos inválidos",
        description: errors.join(", "),
        variant: "destructive",
      });
    }

    if (validFiles.length > 0) {
      setReferenceImages(prev => [...prev, ...validFiles].slice(0, 4)); // Máximo 4 imágenes
    }
  };

  // Función para generar con referencias
  const handleGenerateWithReferences = async () => {
    if (!referencePrompt.trim() || referenceImages.length === 0) {
      toast({
        title: "Datos requeridos",
        description: "Necesitas un prompt y al menos una imagen de referencia",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const result = await editWithReferences({
        prompt: referencePrompt,
        referenceImages,
        size,
      });

      if (result.success && result.image_url) {
        const newMeme: GeneratedMeme = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt: referencePrompt,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentMeme(newMeme);

        toast({
          title: "¡Meme generado!",
          description: "Tu meme con referencias ha sido creado exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating with references:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar con referencias",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para editar con máscara integrada
  const handleEditWithMask = async () => {
    if (!maskPrompt.trim() || !editingImage) {
      toast({
        title: "Datos requeridos",
        description: "Necesitas cargar una imagen y escribir qué quieres cambiar",
        variant: "destructive",
      });
      return;
    }

    const canvas = canvasRef.current;
    const maskCanvas = maskCanvasRef.current;

    if (!canvas || !maskCanvas) {
      toast({
        title: "Error",
        description: "Canvas no disponible",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Convertir canvas a archivos
      const imageBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/png');
      });

      const maskBlob = await new Promise<Blob>((resolve) => {
        maskCanvas.toBlob((blob) => resolve(blob!), 'image/png');
      });

      // Crear archivos File
      const imageFile = new File([imageBlob], 'image.png', { type: 'image/png' });
      const maskFile = new File([maskBlob], 'mask.png', { type: 'image/png' });

      const result = await editWithMask({
        prompt: maskPrompt,
        image: imageFile,
        mask: maskFile,
      });

      if (result.success && result.image_url) {
        const newMeme: GeneratedMeme = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt: maskPrompt,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentMeme(newMeme);
        setMaskPrompt("");

        toast({
          title: "¡Meme editado!",
          description: "Los cambios han sido aplicados exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error editing with mask:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al editar el meme",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para descargar imagen
  const handleDownload = async () => {
    if (!currentMeme?.image_url) return;

    try {
      const response = await fetch(currentMeme.image_url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `meme-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Descarga iniciada",
        description: "El meme se está descargando",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo descargar la imagen",
        variant: "destructive",
      });
    }
  };

  // Función para compartir
  const handleShare = async () => {
    if (!currentMeme?.image_url) return;

    try {
      if (navigator.share) {
        await navigator.share({
          title: "Mi Meme",
          text: currentMeme.prompt,
          url: currentMeme.image_url,
        });
      } else {
        // Fallback: copiar al portapapeles
        await navigator.clipboard.writeText(currentMeme.image_url);
        toast({
          title: "Enlace copiado",
          description: "El enlace del meme se copió al portapapeles",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo compartir la imagen",
        variant: "destructive",
      });
    }
  };

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!currentMeme?.image_url) return;

    try {
      if (currentMemeSaved) {
        // Quitar de favoritos
        const savedMeme = savedMemes.find(meme => meme.image_url === currentMeme.image_url);
        if (savedMeme) {
          const filteredMemes = savedMemes.filter(meme => meme.id !== savedMeme.id);
          setSavedMemes(filteredMemes);
          setCurrentMemeSaved(false);

          toast({
            title: "💔 Eliminado de favoritos",
            description: "El meme ha sido eliminado de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const memeData = {
          image_url: currentMeme.image_url,
          prompt: currentMeme.prompt,
          revised_prompt: currentMeme.revised_prompt,
          metadata: currentMeme.metadata,
          type: (currentMeme.metadata?.type || "basic") as "basic" | "reference" | "mask_edit",
        };

        const newMeme = createSavedMeme(memeData);
        const updatedMemes = [newMeme, ...savedMemes].slice(0, 50); // Limitar a 50

        setSavedMemes(updatedMemes);
        setCurrentMemeSaved(true);

        toast({
          title: "❤️ ¡Guardado en favoritos!",
          description: "Meme guardado exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar el meme. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [currentMeme, currentMemeSaved, savedMemes, setSavedMemes, toast]);

  // Verificar si el meme actual está guardado
  React.useEffect(() => {
    if (currentMeme?.image_url) {
      setCurrentMemeSaved(isMemeSaved(currentMeme.image_url, savedMemes));
    }
  }, [currentMeme, savedMemes]);

  return (
    <DashboardLayout pageTitle="Crear Meme">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-purple-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Smile className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Crear Meme
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Crea memes divertidos y virales con IA. Usa formatos populares, plantillas interactivas y referencias visuales.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Sparkles className="w-3 h-3 mr-1" />
                  OpenAI GPT-Image-1
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Brush className="w-3 h-3 mr-1" />
                  Editor integrado
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <ImageIcon className="w-3 h-3 mr-1" />
                  Referencias visuales
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Edit3 className="w-3 h-3 mr-1" />
                  Formatos populares
                </Badge>
              </div>
            </div>
          </div>

          {/* Tip sobre referencias */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mb-6"
          >
            <div className="flex items-start gap-3">
              <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-1">💡 Tip Pro: Usa Referencias</h3>
                <p className="text-blue-700 text-sm">
                  Para mejores resultados, sube imágenes de referencia de memes que te gusten.
                  La IA analizará el formato, estilo y estructura para crear memes más precisos y divertidos.
                </p>
              </div>
            </div>
          </motion.div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-1"
          >
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="h-5 w-5 text-purple-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Configura y genera tu meme perfecto
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="generate" className="w-full">
                  <TabsList className="grid w-full grid-cols-3 mb-6">
                    <TabsTrigger value="generate" className="text-xs">
                      <Wand2 className="h-3 w-3 mr-1" />
                      Generar
                    </TabsTrigger>
                    <TabsTrigger value="references" className="text-xs">
                      <ImageIcon className="h-3 w-3 mr-1" />
                      Referencias
                    </TabsTrigger>
                    <TabsTrigger value="edit" className="text-xs">
                      <Edit3 className="h-3 w-3 mr-1" />
                      Editar
                    </TabsTrigger>
                  </TabsList>

                  {/* Tab: Generar */}
                  <TabsContent value="generate" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción del Meme</Label>
                      <Textarea
                        placeholder="Describe el meme que quieres crear..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">
                        Tamaño del Meme
                        <span className="ml-2 text-xs text-gray-500">
                          (Actual: {sizeOptions.find(opt => opt.value === size)?.label})
                        </span>
                      </Label>
                      <div className="grid grid-cols-1 gap-2">
                        {sizeOptions.map((option) => (
                          <Button
                            key={option.value}
                            variant={size === option.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSize(option.value as "1024x1024" | "1536x1024" | "1024x1536")}
                            className={`justify-start transition-all ${
                              size === option.value
                                ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                                : "hover:bg-purple-50 hover:border-purple-300"
                            }`}
                          >
                            <span className="mr-2 text-lg">{option.icon}</span>
                            <div className="text-left">
                              <div className="font-medium">{option.label.split(' (')[0]}</div>
                              <div className="text-xs opacity-75">
                                {option.label.match(/\(([^)]+)\)/)?.[1]}
                              </div>
                            </div>
                            {size === option.value && (
                              <div className="ml-auto">
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                              </div>
                            )}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim()}
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Smile className="h-4 w-4 mr-2" />
                          Generar Meme
                        </>
                      )}
                    </Button>

                    {/* Prompts de ejemplo */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600">Ejemplos:</Label>
                      <div className="space-y-1">
                        {examplePrompts.slice(0, 3).map((example, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            onClick={() => setPrompt(example)}
                            className="w-full text-left text-xs h-auto p-2 justify-start"
                          >
                            {example}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  {/* Tab: Referencias */}
                  <TabsContent value="references" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción del Meme</Label>
                      <Textarea
                        placeholder="Describe el tema de tu meme. Las imágenes de referencia se analizarán automáticamente para aplicar su estilo visual, especifica si se debe usar tal cuál, solo el estilo, etc.."
                        value={referencePrompt}
                        onChange={(e) => setReferencePrompt(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imágenes de Referencia (máx. 4)</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={fileInputRef}
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => handleReferenceFiles(e.target.files)}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Subir Imágenes
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB cada una
                        </p>
                      </div>

                      {/* Mostrar imágenes de referencia */}
                      {referenceImages.length > 0 && (
                        <div className="grid grid-cols-2 gap-2">
                          {referenceImages.map((file, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Referencia ${index + 1}`}
                                className="w-full h-20 object-cover rounded-lg"
                              />
                              <Button
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => {
                                  setReferenceImages(prev => prev.filter((_, i) => i !== index));
                                }}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">
                        Tamaño del Meme
                        <span className="ml-2 text-xs text-gray-500">
                          (Actual: {sizeOptions.find(opt => opt.value === size)?.label})
                        </span>
                      </Label>
                      <div className="grid grid-cols-1 gap-2">
                        {sizeOptions.map((option) => (
                          <Button
                            key={option.value}
                            variant={size === option.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSize(option.value as "1024x1024" | "1536x1024" | "1024x1536")}
                            className={`justify-start transition-all ${
                              size === option.value
                                ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                                : "hover:bg-purple-50 hover:border-purple-300"
                            }`}
                          >
                            <span className="mr-2 text-lg">{option.icon}</span>
                            <div className="text-left">
                              <div className="font-medium">{option.label.split(' (')[0]}</div>
                              <div className="text-xs opacity-75">
                                {option.label.match(/\(([^)]+)\)/)?.[1]}
                              </div>
                            </div>
                            {size === option.value && (
                              <div className="ml-auto">
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                              </div>
                            )}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <Button
                      onClick={handleGenerateWithReferences}
                      disabled={isGenerating || !referencePrompt.trim() || referenceImages.length === 0}
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <ImageIcon className="h-4 w-4 mr-2" />
                          Generar con Referencias
                        </>
                      )}
                    </Button>
                  </TabsContent>

                  {/* Tab: Editar */}
                  <TabsContent value="edit" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Cargar Imagen para Editar</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={imageInputRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const validation = validateImageFile(file);
                              if (validation.valid) {
                                handleLoadImageForEdit(file);
                              } else {
                                toast({
                                  title: "Archivo inválido",
                                  description: validation.error,
                                  variant: "destructive",
                                });
                              }
                            }
                          }}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => imageInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Cargar Imagen
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB
                        </p>
                      </div>
                    </div>

                    {editingImage && (
                      <>
                        <div className="space-y-3">
                          <Label className="text-sm font-medium">Qué quieres cambiar</Label>
                          <Textarea
                            placeholder="Describe qué quieres cambiar en las áreas marcadas..."
                            value={maskPrompt}
                            onChange={(e) => setMaskPrompt(e.target.value)}
                            className="min-h-[80px] resize-none"
                          />
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Herramientas de Edición</Label>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowMask(!showMask)}
                              >
                                {showMask ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={clearMask}
                              >
                                <RotateCcw className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-xs text-gray-600">Tamaño del pincel: {brushSize}px</Label>
                            <Slider
                              value={[brushSize]}
                              onValueChange={(value) => setBrushSize(value[0])}
                              max={50}
                              min={5}
                              step={5}
                              className="w-full"
                            />
                          </div>
                        </div>

                        <Button
                          onClick={handleEditWithMask}
                          disabled={isGenerating || !maskPrompt.trim()}
                          className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                        >
                          {isGenerating ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Editando...
                            </>
                          ) : (
                            <>
                              <Edit3 className="h-4 w-4 mr-2" />
                              Aplicar Cambios
                            </>
                          )}
                        </Button>
                      </>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>

          {/* Área de Visualización */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="latest" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Última Generación
                </TabsTrigger>
                <TabsTrigger value="saved" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Guardados ({savedMemes.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="latest" className="space-y-6">
              {/* Meme Generado */}
              {currentMeme && (
                <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-purple-600" />
                        Meme Generado
                      </CardTitle>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleToggleFavorite}
                          className={currentMemeSaved ? "text-red-600 border-red-200" : ""}
                        >
                          {currentMemeSaved ? (
                            <Heart className="h-4 w-4 fill-current" />
                          ) : (
                            <HeartOff className="h-4 w-4" />
                          )}
                        </Button>
                        <Button variant="outline" size="sm" onClick={handleShare}>
                          <Share2 className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={handleDownload}>
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="relative group">
                      <img
                        src={currentMeme.image_url}
                        alt="Meme generado"
                        className="w-full rounded-lg shadow-lg"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                    </div>

                    {/* Información del meme */}
                    <div className="mt-4 space-y-2">
                      <div className="flex items-start gap-2">
                        <Badge variant="secondary" className="mt-0.5">Concepto</Badge>
                        <p className="text-sm text-gray-600 flex-1">{currentMeme.prompt}</p>
                      </div>

                      {currentMeme.revised_prompt && (
                        <div className="flex items-start gap-2">
                          <Badge variant="outline" className="mt-0.5">Revisado</Badge>
                          <p className="text-xs text-gray-500 flex-1">{currentMeme.revised_prompt}</p>
                        </div>
                      )}

                      {currentMeme.metadata && (
                        <div className="flex items-center gap-2 text-xs text-gray-400">
                          <span>Modelo: {currentMeme.metadata.model}</span>
                          {currentMeme.metadata.size && (
                            <span>• Tamaño: {currentMeme.metadata.size}</span>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Editor de Máscara */}
              {editingImage && (
                <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2">
                      <Brush className="h-5 w-5 text-purple-600" />
                      Editor de Máscara
                    </CardTitle>
                    <CardDescription>
                      Pinta las áreas que quieres cambiar en blanco
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="relative flex justify-center">
                      <div className="relative">
                        <canvas
                          ref={canvasRef}
                          className="rounded-lg border border-gray-200"
                          style={{ zIndex: 1, display: 'block' }}
                        />
                        <canvas
                          ref={maskCanvasRef}
                          className={`absolute inset-0 rounded-lg cursor-crosshair ${
                            showMask ? 'opacity-50' : 'opacity-0'
                          }`}
                          style={{
                            zIndex: 2,
                            mixBlendMode: 'multiply',
                            transition: 'opacity 0.2s ease'
                          }}
                          onMouseDown={startDrawing}
                          onMouseMove={draw}
                          onMouseUp={stopDrawing}
                          onMouseLeave={stopDrawing}
                        />
                      </div>
                    </div>

                    <div className="mt-4 text-xs text-gray-500 space-y-1">
                      <p>• Pinta en blanco las áreas que quieres cambiar</p>
                      <p>• Usa el control deslizante para ajustar el tamaño del pincel</p>
                      <p>• Puedes ocultar/mostrar la máscara con el botón del ojo</p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Estado vacío */}
              {!currentMeme && !editingImage && (
                <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                  <CardContent className="flex flex-col items-center justify-center py-16">
                    <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mb-6">
                      <Smile className="h-12 w-12 text-purple-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      ¡Crea tu primer meme!
                    </h3>
                    <p className="text-gray-600 text-center max-w-md mb-6">
                      Describe una situación divertida, usa imágenes de referencia o carga una imagen para editar.
                    </p>
                    <div className="flex flex-wrap gap-2 justify-center">
                      <Badge variant="secondary">Drake Format</Badge>
                      <Badge variant="secondary">Distracted Boyfriend</Badge>
                      <Badge variant="secondary">Woman Yelling at Cat</Badge>
                      <Badge variant="secondary">This is Fine</Badge>
                    </div>
                  </CardContent>
                </Card>
              )}
              </TabsContent>

              <TabsContent value="saved" className="space-y-6">
                {savedMemes.length === 0 ? (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center mb-6">
                        <Heart className="h-12 w-12 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        No tienes memes guardados
                      </h3>
                      <p className="text-gray-600 text-center max-w-md">
                        Los memes que marques como favoritos aparecerán aquí para acceso rápido.
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {savedMemes.map((savedMeme) => (
                      <Card key={savedMeme.id} className="shadow-xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
                        <div className="relative group">
                          <img
                            src={savedMeme.image_url}
                            alt="Meme guardado"
                            className="w-full h-48 object-cover"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors"></div>
                        </div>

                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-start gap-2">
                              <Badge variant="secondary" className="mt-0.5 text-xs">
                                {savedMeme.type === "basic" ? "Básico" :
                                 savedMeme.type === "reference" ? "Referencias" : "Editado"}
                              </Badge>
                              <p className="text-sm text-gray-600 flex-1 line-clamp-2">
                                {savedMeme.prompt}
                              </p>
                            </div>

                            <div className="text-xs text-gray-400">
                              {new Date(savedMeme.timestamp).toLocaleDateString('es-ES', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>

                            {/* Botones de acción */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  // Cargar el meme en la vista principal
                                  setCurrentMeme({
                                    id: savedMeme.id,
                                    image_url: savedMeme.image_url,
                                    prompt: savedMeme.prompt,
                                    revised_prompt: savedMeme.revised_prompt,
                                    metadata: savedMeme.metadata,
                                    timestamp: savedMeme.timestamp,
                                  });

                                  // Cambiar a la pestaña "Última Generación"
                                  setMainTab("latest");

                                  toast({
                                    title: "🖼️ Meme cargado",
                                    description: "Meme cargado en la vista principal.",
                                  });
                                }}
                                className="flex-1"
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={async () => {
                                  try {
                                    const response = await fetch(savedMeme.image_url);
                                    const blob = await response.blob();
                                    const url = window.URL.createObjectURL(blob);
                                    const a = document.createElement("a");
                                    a.href = url;
                                    a.download = `meme-${savedMeme.id}.png`;
                                    document.body.appendChild(a);
                                    a.click();
                                    window.URL.revokeObjectURL(url);
                                    document.body.removeChild(a);

                                    toast({
                                      title: "Descarga iniciada",
                                      description: "El meme se está descargando",
                                    });
                                  } catch (error) {
                                    toast({
                                      title: "Error",
                                      description: "No se pudo descargar la imagen",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => {
                                  const filteredMemes = savedMemes.filter(m => m.id !== savedMeme.id);
                                  setSavedMemes(filteredMemes);

                                  toast({
                                    title: "🗑️ Meme eliminado",
                                    description: "El meme ha sido eliminado de tus guardados.",
                                  });
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
}
