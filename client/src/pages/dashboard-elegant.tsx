"use client"
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import { useState, useEffect, useRef } from "react";
import {
  MessageCircle,
  ArrowRight,
  Sparkles,
  Plus,
  Eye,
  TrendingUp,
  ArrowLeft,
  Zap,
  Heart,
  Star,
  ChevronRight,
  BarChart3,
  Users,
  Target,
  Palette,
  PenTool,
  Search
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

// Importamos la imagen de Emma
import EmmaProfile from "@/assets/emma-profile.png";

// Emma brand colors for consistent styling - Más vibrantes y visibles
const EMMA_COLORS = {
  primary: "#3018ef",        // Azul Emma principal
  primaryLight: "#6366f1",   // Azul más claro y vibrante
  secondary: "#dd3a5a",      // <PERSON> principal
  secondaryLight: "#f472b6", // Rosa más claro y vibrante
  purple: "#8b5cf6",         // Púrpura vibrante
  purpleLight: "#a78bfa",    // Púrpura claro
  green: "#10b981",          // Verde vibrante
  greenLight: "#34d399",     // Verde claro
  orange: "#f59e0b",         // Naranja vibrante
  orangeLight: "#fbbf24",    // Naranja claro
  cyan: "#06b6d4",           // Cian vibrante
  cyanLight: "#22d3ee",      // Cian claro
};

// Helper function to get Emma brand colors with different intensities - Más vibrantes
function getEmmaColor(colorType: string, intensity: "base" | "light" | "dark" = "base"): string {
  const colorMap: Record<string, Record<string, string>> = {
    primary: {
      base: EMMA_COLORS.primary,
      light: EMMA_COLORS.primaryLight,
      dark: "#1e1b8b",
    },
    secondary: {
      base: EMMA_COLORS.secondary,
      light: EMMA_COLORS.secondaryLight,
      dark: "#be185d",
    },
    purple: {
      base: EMMA_COLORS.purple,
      light: EMMA_COLORS.purpleLight,
      dark: "#7c3aed",
    },
    green: {
      base: EMMA_COLORS.green,
      light: EMMA_COLORS.greenLight,
      dark: "#059669",
    },
    orange: {
      base: EMMA_COLORS.orange,
      light: EMMA_COLORS.orangeLight,
      dark: "#d97706",
    },
    cyan: {
      base: EMMA_COLORS.cyan,
      light: EMMA_COLORS.cyanLight,
      dark: "#0891b2",
    },
  };

  return colorMap[colorType]?.[intensity] || EMMA_COLORS.primary;
}

// Componente QuickActionsCarousel con scroll horizontal deslizable
function QuickActionsCarousel({ quickActions, onNavigate }: {
  quickActions: any[],
  onNavigate: (path: string) => void
}) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!scrollContainerRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - scrollContainerRef.current.offsetLeft);
    setScrollLeft(scrollContainerRef.current.scrollLeft);
    scrollContainerRef.current.style.cursor = 'grabbing';
    scrollContainerRef.current.style.userSelect = 'none';
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return;
    e.preventDefault();
    const x = e.pageX - scrollContainerRef.current.offsetLeft;
    const walk = (x - startX) * 2; // Velocidad de scroll
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.cursor = 'grab';
      scrollContainerRef.current.style.userSelect = 'auto';
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    if (scrollContainerRef.current) {
      scrollContainerRef.current.style.cursor = 'grab';
      scrollContainerRef.current.style.userSelect = 'auto';
    }
  };

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1.0 }}
      className="py-16"
    >
      <div className="container mx-auto">
        <div className="mb-8 flex items-end justify-between md:mb-14 lg:mb-16">
          <div className="flex flex-col gap-4">
            <h2 className="text-3xl font-bold md:text-4xl lg:text-5xl bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              Acciones Rápidas
            </h2>
            <p className="max-w-lg text-gray-600 text-lg">
              Tareas más comunes para empezar ahora mismo • Desliza para ver más
            </p>
          </div>
        </div>
      </div>
      <div className="w-full">
        <div
          ref={scrollContainerRef}
          className="flex gap-5 overflow-x-auto no-scrollbar cursor-grab select-none px-4 md:px-8 pb-4"
          style={{
            scrollBehavior: 'smooth'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
        >
          {quickActions.map((action, index) => (
            <div
              key={index}
              className="flex-shrink-0 w-[320px] lg:w-[360px]"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.1 + index * 0.1 }}
                className="group cursor-pointer rounded-2xl"
                onClick={(e) => {
                  if (!isDragging) {
                    onNavigate(action.path);
                  }
                }}
                whileHover={{
                  y: -8,
                  scale: 1.02,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
              >
                <div className="group relative h-full min-h-[20rem] max-w-full overflow-hidden rounded-2xl bg-white/90 backdrop-blur-md border border-white/20 hover:border-white/40 shadow-xl hover:shadow-2xl transition-all duration-500">
                  {/* Gradient background header */}
                  <div
                    className="absolute top-0 left-0 right-0 h-32 opacity-90"
                    style={{
                      background: `linear-gradient(135deg, ${getEmmaColor(action.colorType || "primary", "base")}, ${getEmmaColor(action.colorType || "primary", "light")})`,
                    }}
                  >
                    {/* Glassmorphism overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20"></div>

                    {/* Modern geometric patterns */}
                    <div className="absolute right-0 top-0 w-24 h-24 bg-white/10 rounded-bl-[2rem] backdrop-blur-sm"></div>
                    <div className="absolute left-0 bottom-0 w-12 h-12 bg-white/15 rounded-tr-[1rem]"></div>
                  </div>

                  {/* Content */}
                  <div className="relative z-10 flex flex-col justify-center items-center p-8 pt-16">
                    <motion.div
                      className="w-20 h-20 bg-white/90 backdrop-blur-md rounded-2xl flex items-center justify-center mb-6 border border-white/30 shadow-xl"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <span className="text-3xl">{action.preview}</span>
                    </motion.div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3 text-center group-hover:text-gray-700 transition-colors">
                      {action.title}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed text-center mb-6">
                      {action.description}
                    </p>
                    <div className="flex items-center text-sm font-semibold text-gray-500 group-hover:text-gray-700 transition-colors">
                      Empezar{" "}
                      <ArrowRight className="ml-2 size-4 transition-transform group-hover:translate-x-1" />
                    </div>
                  </div>

                  {/* Hover effect overlay */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-transparent via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </div>
              </motion.div>
            </div>
          ))}
        </div>
      </div>
    </motion.section>
  );
}

function DashboardContent() {
  const [, navigate] = useLocation();

  // Acciones rápidas con diseño moderno y colores Emma
  const quickActions = [
    {
      title: "Editor Visual",
      description: "Diseña contenido profesional con Polotno Studio",
      path: "/visual-editor",
      preview: "🎨",
      colorType: "purple"
    },
    {
      title: "Analizar Audiencia",
      description: "Descubre insights de tu buyer persona",
      path: "/dashboard/herramientas/buyer-persona-generator",
      preview: "👥",
      colorType: "primary"
    },
    {
      title: "Optimizar Headlines",
      description: "Títulos que aumentan el CTR",
      path: "/dashboard/herramientas/headline-analyzer",
      preview: "📝",
      colorType: "green"
    },
    {
      title: "Generar Imágenes",
      description: "Crea visuales profesionales con IA",
      path: "/image-generator",
      preview: "🖼️",
      colorType: "secondary"
    },
    {
      title: "SEO Analyzer",
      description: "Optimiza tu contenido para buscadores",
      path: "/dashboard/herramientas/seo-analyzer",
      preview: "🔍",
      colorType: "cyan"
    },
    {
      title: "Emma AI Chat",
      description: "Conversa con Emma para cualquier tarea",
      path: "/emma-ai",
      preview: "🤖",
      colorType: "orange"
    }
  ];

  // Herramientas organizadas por flujo de trabajo con colores Emma vibrantes
  const workflowSections = [
    {
      title: "🔍 Investigar & Analizar",
      description: "Comprende tu mercado y audiencia",
      tools: [
        {
          title: "Buyer Personas",
          description: "Perfiles detallados de clientes ideales",
          path: "/dashboard/herramientas/buyer-persona-generator",
          preview: "👤",
          colorType: "primary" // Azul Emma vibrante
        },
        {
          title: "Focus Groups",
          description: "Simula grupos de enfoque virtuales",
          path: "/dashboard/herramientas/focus-group-simulator",
          preview: "💬",
          colorType: "green" // Verde vibrante
        },
        {
          title: "Análisis de Diseño",
          description: "Evalúa complejidad y usabilidad",
          path: "/dashboard/herramientas/design-complexity-analyzer",
          preview: "📐",
          colorType: "purple" // Púrpura vibrante
        }
      ]
    },
    {
      title: "✍️ Crear Contenido",
      description: "Genera copy y contenido de alta calidad",
      tools: [
        {
          title: "Emma AI Chat",
          description: "Conversa con Emma para cualquier tarea",
          path: "/emma-ai",
          preview: "🤖",
          colorType: "secondary" // Rosa Emma vibrante
        },
        {
          title: "Headlines Optimizer",
          description: "Optimiza títulos para máximo impacto",
          path: "/dashboard/herramientas/headline-analyzer",
          preview: "📰",
          colorType: "orange" // Naranja vibrante
        },
        {
          title: "SEO Analyzer",
          description: "Optimiza contenido para buscadores",
          path: "/dashboard/herramientas/seo-analyzer",
          preview: "🔍",
          colorType: "cyan" // Cian vibrante
        }
      ]
    },
    {
      title: "🎨 Diseñar & Visual",
      description: "Crea contenido visual profesional",
      tools: [
        {
          title: "Generador de Paletas",
          description: "Crea paletas de colores profesionales",
          path: "/dashboard/herramientas/color-palette-generator",
          preview: "🎨",
          colorType: "secondary" // Rosa Emma vibrante
        },
        {
          title: "Generador de Imágenes",
          description: "Crea imágenes con IA avanzada",
          path: "/image-generator",
          preview: "🖼️",
          colorType: "purple" // Púrpura vibrante
        },
        {
          title: "Mood Board",
          description: "Tableros de inspiración visual",
          path: "/dashboard/herramientas/mood-board",
          preview: "📌",
          colorType: "primary" // Azul Emma vibrante
        }
      ]
    }
  ];

  return (
    <div className="space-y-12">
      {/* HERO SECTION MODERNIZADO */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative overflow-hidden rounded-2xl backdrop-blur-xl mb-8"
      >
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

        {/* Floating elements with glassmorphism */}
        <motion.div
          className="absolute right-0 bottom-0 transform translate-y-1/4 -translate-x-10 hidden lg:block"
          initial={{ opacity: 0, x: 100, rotate: -10 }}
          animate={{ opacity: 1, x: 0, rotate: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
        >
          <div className="relative w-72 h-72">
            <motion.div
              className="absolute w-32 h-32 bg-white/20 backdrop-blur-md rounded-2xl -top-24 -left-16 transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30"
              animate={{ rotate: [12, 18, 12] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            >
              <span className="text-5xl">✨</span>
            </motion.div>
            <motion.div
              className="absolute w-36 h-36 bg-white/15 backdrop-blur-md rounded-2xl -top-8 left-16 transform -rotate-6 flex items-center justify-center shadow-2xl border border-white/20"
              animate={{ rotate: [-6, -12, -6] }}
              transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
            >
              <span className="text-5xl">📊</span>
            </motion.div>
            <motion.div
              className="absolute w-28 h-28 bg-white/25 backdrop-blur-md rounded-2xl top-20 -left-8 transform rotate-45 flex items-center justify-center shadow-2xl border border-white/40"
              animate={{ rotate: [45, 50, 45] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 2 }}
            >
              <span className="text-4xl">🔍</span>
            </motion.div>
          </div>
        </motion.div>

        <div className="relative px-8 py-16 md:py-20 md:px-12">
          <div className="max-w-5xl">
            {/* Contenido principal */}
            <div className="flex flex-col lg:flex-row items-center gap-12">
              <div className="flex-1">
                <div className="flex items-center gap-6 mb-8">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                  >
                    <Avatar className="h-20 w-20 border-2 border-white/30 shadow-xl backdrop-blur-md">
                      <AvatarImage src={EmmaProfile} alt="Emma AI" />
                      <AvatarFallback className="bg-white/20 backdrop-blur-md text-white text-2xl font-bold">
                        E
                      </AvatarFallback>
                    </Avatar>
                  </motion.div>
                  <div>
                    <motion.span
                      className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-4 py-2 rounded-full mb-4 border border-white/30 text-sm"
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      whileHover={{ scale: 1.05 }}
                    >
                      <Sparkles className="inline-block w-4 h-4 mr-2" />
                      Potenciado con IA
                    </motion.span>
                    <motion.h1
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-4xl lg:text-6xl font-black mb-2 leading-tight"
                    >
                      <span className="text-white">El Marketing Ya </span>
                      <span className="bg-gradient-to-r from-[#dd3a5a] via-[#f472b6] to-[#fbbf24] bg-clip-text text-transparent">
                        Cambió
                      </span>
                    </motion.h1>
                    <motion.p
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 }}
                      className="text-lg text-white/90 font-light"
                    >
                      Marketing • Diseño • Analytics • Growth
                    </motion.p>
                  </div>
                </div>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="text-xl text-white/90 mb-8 leading-relaxed max-w-3xl font-light"
                >
                  Soy Emma, tu agente de marketing con IA. Creo contenido viral, analizo competencia
                  y genero estrategias que funcionan. Simple, rápido, efectivo.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                  className="flex flex-wrap gap-4"
                >
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      onClick={() => navigate("/emma-ai")}
                      size="lg"
                      className="bg-white/90 backdrop-blur-md hover:bg-white text-[#3018ef] font-bold px-8 py-3 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-white/30"
                    >
                      <MessageCircle className="mr-2 h-5 w-5" />
                      Hablar con Emma
                    </Button>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                    <Button
                      onClick={() => navigate("/dashboard/herramientas-marketing")}
                      variant="outline"
                      size="lg"
                      className="border-2 border-white text-white hover:bg-white hover:text-[#3018ef] font-bold px-8 py-3 rounded-xl backdrop-blur-md transition-all duration-300 shadow-xl hover:shadow-2xl bg-white/10 hover:bg-white"
                    >
                      Ver herramientas
                    </Button>
                  </motion.div>
                </motion.div>
              </div>

              {/* Métricas elegantes modernizadas */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 }}
                className="lg:w-80"
              >
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { value: "+300%", label: "ROI Promedio", icon: "📈" },
                    { value: "24/7", label: "Disponible", icon: "⏰" },
                    { value: "10x", label: "Más Rápida", icon: "⚡" },
                    { value: "95%", label: "Precisión", icon: "🎯" }
                  ].map((stat, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ scale: 1.05, y: -2 }}
                      transition={{ type: "spring", stiffness: 400, damping: 25 }}
                    >
                      <Card className="border-white/20 bg-white/20 backdrop-blur-md hover:bg-white/30 transition-all duration-300 shadow-xl">
                        <CardContent className="p-4 text-center">
                          <div className="text-2xl mb-2">{stat.icon}</div>
                          <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                          <div className="text-sm text-white/80">{stat.label}</div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* ACCIONES RÁPIDAS CON CAROUSEL */}
      <QuickActionsCarousel
        quickActions={quickActions}
        onNavigate={navigate}
      />

      {/* FLUJO DE TRABAJO COMPLETO MODERNIZADO */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.5 }}
      >
        <div className="mb-8">
          <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
            Flujo de Trabajo Completo
          </h2>
          <p className="text-gray-600 text-lg">Herramientas organizadas por proceso de marketing</p>
        </div>

        <div className="space-y-8">
          {workflowSections.map((section, sectionIndex) => (
            <motion.div
              key={sectionIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.6 + sectionIndex * 0.2 }}
            >
              <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                      <span className="mr-3">{section.title}</span>
                    </h3>
                    <p className="text-gray-600 text-base">{section.description}</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {section.tools.map((tool, toolIndex) => (
                      <motion.div
                        key={toolIndex}
                        whileHover={{
                          scale: 1.05,
                          y: -8,
                          transition: { type: "spring", stiffness: 400, damping: 25 }
                        }}
                        className="group"
                      >
                        <Card
                          className="cursor-pointer border-2 border-transparent hover:border-white/40 bg-white/95 backdrop-blur-md hover:bg-white hover:shadow-2xl transition-all duration-300 overflow-hidden rounded-2xl"
                          onClick={() => navigate(tool.path)}
                          style={{
                            background: `linear-gradient(135deg, ${getEmmaColor(tool.colorType || "primary", "base")}15, ${getEmmaColor(tool.colorType || "primary", "light")}10, white)`,
                          }}
                        >
                          <CardContent className="p-6 relative">
                            {/* Vibrant gradient header */}
                            <div
                              className="absolute top-0 left-0 right-0 h-20 rounded-t-2xl"
                              style={{
                                background: `linear-gradient(135deg, ${getEmmaColor(tool.colorType || "primary", "base")}, ${getEmmaColor(tool.colorType || "primary", "light")})`,
                              }}
                            >
                              {/* Glassmorphism overlay */}
                              <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/10 rounded-t-2xl"></div>

                              {/* Decorative elements */}
                              <div className="absolute right-0 top-0 w-16 h-16 bg-white/20 rounded-bl-2xl"></div>
                              <div className="absolute left-0 bottom-0 w-8 h-8 bg-white/30 rounded-tr-xl"></div>
                            </div>

                            {/* Icon container with vibrant styling */}
                            <motion.div
                              className="relative z-10 w-20 h-20 rounded-2xl flex items-center justify-center mb-6 mt-4 border-2 shadow-xl"
                              style={{
                                background: `linear-gradient(135deg, ${getEmmaColor(tool.colorType || "primary", "base")}, ${getEmmaColor(tool.colorType || "primary", "light")})`,
                                borderColor: `${getEmmaColor(tool.colorType || "primary", "base")}40`,
                              }}
                              whileHover={{ scale: 1.15, rotate: 10 }}
                              transition={{ type: "spring", stiffness: 400, damping: 25 }}
                            >
                              <span className="text-3xl filter drop-shadow-sm">{tool.preview}</span>
                            </motion.div>

                            <h4 className="font-bold text-gray-900 mb-3 text-lg group-hover:text-gray-700 transition-colors relative z-10">
                              {tool.title}
                            </h4>
                            <p className="text-gray-600 text-sm leading-relaxed relative z-10 mb-4">
                              {tool.description}
                            </p>

                            {/* Action indicator */}
                            <div className="flex items-center text-sm font-semibold relative z-10 group-hover:translate-x-2 transition-transform duration-300"
                              style={{ color: getEmmaColor(tool.colorType || "primary", "base") }}
                            >
                              Usar herramienta
                              <ChevronRight className="ml-1 w-4 h-4" />
                            </div>

                            {/* Enhanced hover effect */}
                            <div
                              className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"
                              style={{
                                background: `linear-gradient(135deg, ${getEmmaColor(tool.colorType || "primary", "base")}, ${getEmmaColor(tool.colorType || "primary", "light")})`,
                              }}
                            />
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* SUGERENCIAS INTELIGENTES DE EMMA MODERNIZADAS */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2.0 }}
      >
        <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden">
          <CardContent className="p-8 relative">
            {/* Gradient background */}
            <div className="absolute top-0 left-0 right-0 h-24 bg-gradient-to-r from-[#3018ef]/10 via-[#8b5cf6]/10 to-[#dd3a5a]/10 opacity-50" />

            <div className="flex items-start gap-6 relative z-10">
              <motion.div
                whileHover={{ scale: 1.05, rotate: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Avatar className="h-16 w-16 border-2 border-white/30 shadow-xl backdrop-blur-md">
                  <AvatarImage src={EmmaProfile} alt="Emma AI" />
                  <AvatarFallback className="bg-gradient-to-br from-[#3018ef]/20 to-[#dd3a5a]/20 backdrop-blur-md text-gray-700 text-xl font-bold">
                    E
                  </AvatarFallback>
                </Avatar>
              </motion.div>
              <div className="flex-1">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3 flex items-center">
                  <Sparkles className="w-6 h-6 text-[#3018ef] mr-2" />
                  Sugerencias Inteligentes de Emma
                </h3>
                <div className="space-y-4">
                  <motion.div
                    className="bg-white/80 backdrop-blur-md rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.02, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                        <TrendingUp className="h-5 w-5 text-white" />
                      </div>
                      <span className="font-bold text-gray-900">Optimización Sugerida</span>
                    </div>
                    <p className="text-gray-700 mb-4 leading-relaxed">
                      Basándome en tus proyectos recientes, te recomiendo crear un buyer persona
                      más detallado para mejorar la segmentación de tus campañas.
                    </p>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        onClick={() => navigate("/dashboard/herramientas/buyer-persona-generator")}
                        size="sm"
                        className="bg-gradient-to-r from-[#3018ef] to-[#4f46e5] hover:from-[#1e1b8b] hover:to-[#3730a3] text-white font-semibold px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                      >
                        Crear Buyer Persona
                      </Button>
                    </motion.div>
                  </motion.div>

                  <motion.div
                    className="bg-white/80 backdrop-blur-md rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.02, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <Sparkles className="h-5 w-5 text-white" />
                      </div>
                      <span className="font-bold text-gray-900">Tendencia Detectada</span>
                    </div>
                    <p className="text-gray-700 mb-4 leading-relaxed">
                      He notado un aumento del 40% en engagement con contenido visual.
                      ¿Quieres que genere más imágenes para tus próximas campañas?
                    </p>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        onClick={() => navigate("/image-generator")}
                        size="sm"
                        variant="outline"
                        className="border-2 border-[#dd3a5a]/30 text-[#dd3a5a] hover:bg-[#dd3a5a]/5 font-semibold px-4 py-2 rounded-lg backdrop-blur-md transition-all duration-300"
                      >
                        Generar Imágenes
                      </Button>
                    </motion.div>
                  </motion.div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* ACTIVIDAD RECIENTE & PROYECTOS MODERNIZADOS */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 2.2 }}
        >
          <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 h-full">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent flex items-center">
                  <BarChart3 className="w-5 h-5 text-[#3018ef] mr-2" />
                  Actividad Reciente
                </h3>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button variant="ghost" size="sm" className="hover:bg-gray-100 rounded-lg">
                    <Eye className="h-4 w-4 mr-2" />
                    Ver todo
                  </Button>
                </motion.div>
              </div>

              <div className="space-y-4">
                {[
                  { action: "Post generado", time: "Hace 2 horas", type: "success", icon: "📝" },
                  { action: "Análisis completado", time: "Hace 4 horas", type: "info", icon: "📊" },
                  { action: "Imagen creada", time: "Ayer", type: "success", icon: "🖼️" },
                  { action: "Campaña optimizada", time: "Hace 2 días", type: "warning", icon: "🎯" }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center gap-4 p-4 rounded-xl hover:bg-white/60 transition-all duration-300 border border-transparent hover:border-white/30"
                    whileHover={{ scale: 1.02, x: 4 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <div className="text-2xl">{item.icon}</div>
                    <div className="flex-1">
                      <p className="font-semibold text-gray-900">{item.action}</p>
                      <p className="text-sm text-gray-500">{item.time}</p>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${
                      item.type === 'success' ? 'bg-green-500' :
                      item.type === 'info' ? 'bg-blue-500' :
                      item.type === 'warning' ? 'bg-yellow-500' : 'bg-gray-500'
                    }`}></div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 2.4 }}
        >
          <Card className="border-white/20 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-all duration-300 h-full">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent flex items-center">
                  <Target className="w-5 h-5 text-[#dd3a5a] mr-2" />
                  Proyectos Activos
                </h3>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button variant="ghost" size="sm" className="hover:bg-gray-100 rounded-lg">
                    <Plus className="h-4 w-4 mr-2" />
                    Nuevo
                  </Button>
                </motion.div>
              </div>

              <div className="space-y-4">
                {[
                  { name: "Campaña Q1 2024", progress: 75, status: "En progreso", color: "green", icon: "🚀" },
                  { name: "Rediseño de marca", progress: 45, status: "Planificación", color: "blue", icon: "🎨" },
                  { name: "Contenido redes sociales", progress: 90, status: "Casi listo", color: "purple", icon: "📱" }
                ].map((project, index) => (
                  <motion.div
                    key={index}
                    className="p-5 border border-white/20 rounded-xl hover:border-white/40 transition-all duration-300 bg-white/60 backdrop-blur-md hover:bg-white/80"
                    whileHover={{ scale: 1.02, y: -2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <span className="text-xl">{project.icon}</span>
                        <h4 className="font-bold text-gray-900">{project.name}</h4>
                      </div>
                      <Badge
                        className={`
                          ${project.color === 'green' ? 'bg-green-100 text-green-700 border-green-200' : ''}
                          ${project.color === 'blue' ? 'bg-blue-100 text-blue-700 border-blue-200' : ''}
                          ${project.color === 'purple' ? 'bg-purple-100 text-purple-700 border-purple-200' : ''}
                          font-semibold px-3 py-1 rounded-full
                        `}
                      >
                        {project.status}
                      </Badge>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3 mb-2 overflow-hidden">
                      <motion.div
                        className={`h-3 rounded-full transition-all duration-500 ${
                          project.color === 'green' ? 'bg-gradient-to-r from-green-500 to-emerald-500' :
                          project.color === 'blue' ? 'bg-gradient-to-r from-blue-500 to-cyan-500' :
                          'bg-gradient-to-r from-purple-500 to-pink-500'
                        }`}
                        initial={{ width: 0 }}
                        animate={{ width: `${project.progress}%` }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                    </div>
                    <p className="text-sm font-semibold text-gray-600">{project.progress}% completado</p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}

function DashboardElegant() {
  return (
    <DashboardLayout pageTitle="Dashboard">
      <DashboardContent />
    </DashboardLayout>
  );
}

export default DashboardElegant;
