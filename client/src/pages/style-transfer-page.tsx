/**
 * Página de Transferencia de Estilo
 * Permite transferir el estilo de una imagen a otra usando Stability AI
 */

import React from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import StyleTransferSimple from '@/components/style-transfer/StyleTransferSimple';

export default function StyleTransferPage() {
  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Transferir Estilo
          </h1>
          <p className="text-gray-600">
            Transfiere el estilo de una imagen de referencia a tu imagen original
          </p>
        </div>

        <StyleTransferSimple />
      </div>
    </DashboardLayout>
  );
}
