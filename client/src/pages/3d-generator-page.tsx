import React, { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { 
  Upload, 
  Download, 
  Boxes, 
  Settings, 
  Info,
  Loader2,
  FileImage,
  Sparkles
} from "lucide-react";
import { motion } from "framer-motion";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  generate3DModel,
  downloadGLBFile,
  validateImageFor3D,
  formatFileSize,
  Generate3DOptions,
  Generate3DResponse
} from "@/services/stability-3d-service";

export default function Generate3DPage() {
  // Estados principales
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("");
  const [result, setResult] = useState<Generate3DResponse | null>(null);

  // Estados de configuración
  const [modelType, setModelType] = useState<'fast' | 'point_aware'>('fast');
  const [textureResolution, setTextureResolution] = useState<'512' | '1024' | '2048'>('1024');
  const [foregroundRatio, setForegroundRatio] = useState([0.85]);
  const [remesh, setRemesh] = useState<'none' | 'triangle' | 'quad'>('none');
  const [vertexCount, setVertexCount] = useState<number>(-1);
  // Point Aware 3D específicos
  const [targetType, setTargetType] = useState<'none' | 'vertex' | 'face'>('none');
  const [targetCount, setTargetCount] = useState<number>(1000);
  const [guidanceScale, setGuidanceScale] = useState([3.0]);
  const [seed, setSeed] = useState<number>(0);

  const { toast } = useToast();

  // Cambiar modelo y ajustar valores por defecto
  const handleModelTypeChange = useCallback((newModelType: 'fast' | 'point_aware') => {
    setModelType(newModelType);

    if (newModelType === 'point_aware') {
      // Point Aware 3D defaults
      setForegroundRatio([1.3]);
      setGuidanceScale([3.0]);
      setTargetCount(1000);
      setSeed(0);
    } else {
      // Fast 3D defaults
      setForegroundRatio([0.85]);
      setVertexCount(-1);
    }
  }, []);

  // Generar seed aleatorio
  const generateRandomSeed = useCallback(() => {
    const randomSeed = Math.floor(Math.random() * 4294967294);
    setSeed(randomSeed);
    toast({
      title: "🎲 Seed aleatorio generado",
      description: `Nuevo seed: ${randomSeed}`,
    });
  }, [toast]);

  // Manejar selección de imagen
  const handleImageSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validar imagen
    const validation = validateImageFor3D(file);
    if (!validation.valid) {
      toast({
        title: "❌ Archivo no válido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setSelectedImage(file);
    
    // Crear preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Limpiar resultado anterior
    setResult(null);
  }, [toast]);

  // Generar modelo 3D
  const handleGenerate3D = useCallback(async () => {
    if (!selectedImage) {
      toast({
        title: "❌ Imagen requerida",
        description: "Por favor, selecciona una imagen primero.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setProgress(0);
    setProgressMessage("Iniciando generación 3D...");
    setResult(null);

    try {
      const options: Generate3DOptions = {
        image: selectedImage,
        modelType,
        textureResolution,
        foregroundRatio: foregroundRatio[0],
        remesh,
        vertexCount: modelType === 'fast' ? (vertexCount === -1 ? undefined : vertexCount) : undefined,
        // Point Aware 3D específicos
        targetType: modelType === 'point_aware' ? targetType : undefined,
        targetCount: modelType === 'point_aware' ? targetCount : undefined,
        guidanceScale: modelType === 'point_aware' ? guidanceScale[0] : undefined,
        seed: modelType === 'point_aware' && seed > 0 ? seed : undefined,
      };

      const response = await generate3DModel(
        options,
        (progressValue, message) => {
          setProgress(progressValue);
          setProgressMessage(message);
        }
      );

      setResult(response);

      toast({
        title: "🎉 ¡Modelo 3D generado!",
        description: `Archivo GLB de ${formatFileSize(response.size_mb || 0)} creado exitosamente.`,
      });

    } catch (error) {
      console.error("Error en generación 3D:", error);
      toast({
        title: "❌ Error en generación",
        description: error instanceof Error ? error.message : "Error desconocido",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
      setProgress(0);
      setProgressMessage("");
    }
  }, [selectedImage, modelType, textureResolution, foregroundRatio, remesh, vertexCount, targetType, targetCount, guidanceScale, seed, toast]);

  // Descargar modelo
  const handleDownload = useCallback(() => {
    if (!result?.model_url || !result?.filename) return;

    try {
      downloadGLBFile(result.model_url, result.filename);
      toast({
        title: "📥 Descarga iniciada",
        description: `Descargando ${result.filename}`,
      });
    } catch (error) {
      toast({
        title: "❌ Error en descarga",
        description: "No se pudo descargar el archivo",
        variant: "destructive",
      });
    }
  }, [result, toast]);

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 p-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <div className="flex items-center justify-center gap-3 mb-4">
              <Boxes className="w-10 h-10 text-red-600" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
                Generación 3D
              </h1>
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Convierte cualquier imagen en un modelo 3D detallado usando IA avanzada
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Panel de configuración */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              {/* Subida de imagen */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileImage className="w-5 h-5" />
                    Imagen de entrada
                  </CardTitle>
                  <CardDescription>
                    Sube una imagen para convertir en modelo 3D (JPEG, PNG, WebP)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Input
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      onChange={handleImageSelect}
                      className="cursor-pointer"
                    />
                    
                    {imagePreview && (
                      <div className="relative">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-full h-48 object-cover rounded-lg border"
                        />
                        <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm">
                          {selectedImage?.name}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Configuración avanzada */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Configuración
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Tipo de modelo */}
                  <div className="space-y-2">
                    <Label>Modelo de generación 3D</Label>
                    <Select value={modelType} onValueChange={(value: any) => handleModelTypeChange(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fast">Stable Fast 3D (2 créditos, ~30-60s)</SelectItem>
                        <SelectItem value="point_aware">Stable Point Aware 3D (4 créditos, ~2-5min)</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="text-sm text-muted-foreground">
                      {modelType === 'fast'
                        ? "Generación rápida con buena calidad general"
                        : "Generación avanzada con mayor control y detalle"
                      }
                    </div>
                  </div>

                  {/* Resolución de textura */}
                  <div className="space-y-2">
                    <Label>Resolución de textura</Label>
                    <Select value={textureResolution} onValueChange={(value: any) => setTextureResolution(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="512">512px (Rápido)</SelectItem>
                        <SelectItem value="1024">1024px (Balanceado)</SelectItem>
                        <SelectItem value="2048">2048px (Alta calidad)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Ratio de primer plano */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Label>Ratio de primer plano</Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="w-4 h-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Controla el padding alrededor del objeto</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Slider
                      value={foregroundRatio}
                      onValueChange={setForegroundRatio}
                      min={modelType === 'point_aware' ? 1.0 : 0.1}
                      max={modelType === 'point_aware' ? 2.0 : 1.0}
                      step={0.05}
                      className="w-full"
                    />
                    <div className="text-sm text-muted-foreground">
                      Valor: {foregroundRatio[0].toFixed(2)}
                      {modelType === 'point_aware' ? ' (1.0-2.0)' : ' (0.1-1.0)'}
                    </div>
                  </div>

                  {/* Tipo de remesh */}
                  <div className="space-y-2">
                    <Label>Algoritmo de remesh</Label>
                    <Select value={remesh} onValueChange={(value: any) => setRemesh(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">Ninguno</SelectItem>
                        <SelectItem value="triangle">Triángulos</SelectItem>
                        <SelectItem value="quad">Cuadriláteros</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Parámetros específicos por modelo */}
                  {modelType === 'fast' ? (
                    // Fast 3D específicos
                    <div className="space-y-2">
                      <Label>Límite de vértices (Fast 3D)</Label>
                      <Input
                        type="number"
                        value={vertexCount === -1 ? "" : vertexCount}
                        onChange={(e) => setVertexCount(e.target.value ? parseInt(e.target.value) : -1)}
                        placeholder="Sin límite"
                        min={-1}
                        max={20000}
                      />
                      <div className="text-sm text-muted-foreground">
                        -1 para sin límite, máximo 20,000
                      </div>
                    </div>
                  ) : (
                    // Point Aware 3D específicos
                    <div className="space-y-4">
                      {/* Target Type */}
                      <div className="space-y-2">
                        <Label>Tipo de simplificación</Label>
                        <Select value={targetType} onValueChange={(value: any) => setTargetType(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">Ninguna</SelectItem>
                            <SelectItem value="vertex">Por vértices</SelectItem>
                            <SelectItem value="face">Por caras</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Target Count */}
                      {targetType !== 'none' && (
                        <div className="space-y-2">
                          <Label>Cantidad objetivo</Label>
                          <Input
                            type="number"
                            value={targetCount}
                            onChange={(e) => setTargetCount(parseInt(e.target.value) || 1000)}
                            min={100}
                            max={20000}
                          />
                          <div className="text-sm text-muted-foreground">
                            Número de {targetType === 'vertex' ? 'vértices' : 'caras'} objetivo (100-20,000)
                          </div>
                        </div>
                      )}

                      {/* Guidance Scale */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Label>Escala de guía</Label>
                          <Tooltip>
                            <TooltipTrigger>
                              <Info className="w-4 h-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Controla el nivel de detalle. Valores bajos = menos detalle, valores altos = posibles artefactos</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <Slider
                          value={guidanceScale}
                          onValueChange={setGuidanceScale}
                          min={1.0}
                          max={10.0}
                          step={0.1}
                          className="w-full"
                        />
                        <div className="text-sm text-muted-foreground">
                          Valor: {guidanceScale[0].toFixed(1)} (1.0-10.0)
                        </div>
                      </div>

                      {/* Seed */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Label>Seed (semilla)</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={generateRandomSeed}
                            className="h-6 px-2 text-xs"
                          >
                            🎲 Aleatorio
                          </Button>
                        </div>
                        <Input
                          type="number"
                          value={seed}
                          onChange={(e) => setSeed(parseInt(e.target.value) || 0)}
                          placeholder="0 para aleatorio"
                          min={0}
                          max={4294967294}
                        />
                        <div className="text-sm text-muted-foreground">
                          0 para aleatorio, máximo 4,294,967,294
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Botón de generación */}
              <Button
                onClick={handleGenerate3D}
                disabled={!selectedImage || isGenerating}
                className="w-full h-12 text-lg"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Generando...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-5 h-5 mr-2" />
                    Generar Modelo 3D
                  </>
                )}
              </Button>

              {/* Progreso */}
              {isGenerating && (
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progreso</span>
                        <span>{progress}%</span>
                      </div>
                      <Progress value={progress} className="w-full" />
                      <p className="text-sm text-muted-foreground">{progressMessage}</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </motion.div>

            {/* Panel de resultado */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Boxes className="w-5 h-5" />
                    Resultado
                  </CardTitle>
                  <CardDescription>
                    Tu modelo 3D aparecerá aquí cuando esté listo
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {result ? (
                    <div className="space-y-4">
                      {/* Información del modelo */}
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h3 className="font-semibold text-green-800 mb-2">✅ Modelo generado exitosamente</h3>
                        <div className="space-y-1 text-sm text-green-700">
                          <p><strong>Archivo:</strong> {result.filename}</p>
                          <p><strong>Tamaño:</strong> {formatFileSize(result.size_mb || 0)}</p>
                          <p><strong>Créditos usados:</strong> {result.metadata?.credits_used || 2}</p>
                        </div>
                      </div>

                      {/* Botón de descarga */}
                      <Button
                        onClick={handleDownload}
                        className="w-full"
                        size="lg"
                      >
                        <Download className="w-5 h-5 mr-2" />
                        Descargar Modelo GLB
                      </Button>

                      {/* Información adicional */}
                      <div className="text-sm text-muted-foreground space-y-1">
                        <p><strong>Modelo:</strong> {result.metadata?.model_name}</p>
                        <p><strong>Resolución:</strong> {result.metadata?.texture_resolution}px</p>
                        <p><strong>Remesh:</strong> {result.metadata?.remesh}</p>
                        <p><strong>Ratio:</strong> {result.metadata?.foreground_ratio}</p>
                        {result.metadata?.model_type === 'point_aware' ? (
                          <>
                            <p><strong>Tipo objetivo:</strong> {result.metadata?.target_type}</p>
                            <p><strong>Cantidad objetivo:</strong> {result.metadata?.target_count}</p>
                            <p><strong>Escala de guía:</strong> {result.metadata?.guidance_scale}</p>
                            <p><strong>Seed:</strong> {result.metadata?.seed}</p>
                          </>
                        ) : (
                          <p><strong>Vértices:</strong> {result.metadata?.vertex_count === -1 ? 'Sin límite' : result.metadata?.vertex_count}</p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                      <Boxes className="w-16 h-16 mb-4 opacity-50" />
                      <p className="text-center">
                        {selectedImage 
                          ? "Configura los parámetros y haz clic en 'Generar Modelo 3D'"
                          : "Sube una imagen para comenzar"
                        }
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}
