/**
 * Página dedicada para la herramienta "Mejorar Calidad" del AI Image Editor
 * Permite mejorar la resolución y calidad de imágenes usando Stability AI
 */

import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  Upload,
  Sparkles,
  Download,
  Copy,
  Heart,
  Zap,
  Info,
  Settings,
  Crown,
  Cpu,
} from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  upscaleImageWithPolling,
  UpscaleOptions,
  getUpscaleModes,
  UpscaleModeInfo,
  StylePresetInfo
} from "@/services/stability-upscale-service";

interface UpscaleImagePageProps {
  uploadedImage: string | null;
  uploadedImageFile: File | null;
  originalFilename: string;
  onBack: () => void;
  onResult: (imageUrl: string) => void;
  onSave?: (imageUrl: string) => void;
}

export default function UpscaleImagePage({
  uploadedImage,
  uploadedImageFile,
  originalFilename,
  onBack,
  onResult,
  onSave,
}: UpscaleImagePageProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Estados principales
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("");
  const [resultImage, setResultImage] = useState<string | null>(null);

  // Estados de configuración
  const [mode, setMode] = useState<"fast" | "conservative" | "creative">("fast");
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [seed, setSeed] = useState<number | undefined>(undefined);
  const [creativity, setCreativity] = useState([0.3]);
  const [stylePreset, setStylePreset] = useState<string>("");
  const [outputFormat, setOutputFormat] = useState<"jpeg" | "png" | "webp">("webp");

  // Estados de información
  const [modes, setModes] = useState<UpscaleModeInfo[]>([]);
  const [stylePresets, setStylePresets] = useState<StylePresetInfo[]>([]);

  // Cargar información de modos al montar
  useEffect(() => {
    const loadModes = async () => {
      try {
        const data = await getUpscaleModes();
        setModes(data.modes);
        setStylePresets(data.stylePresets);
      } catch (error) {
        console.error("Error cargando modos:", error);
      }
    };
    loadModes();
  }, []);

  // Información del modo actual
  const currentModeInfo = modes.find(m => m.id === mode);

  // Manejar procesamiento
  const handleProcess = async () => {
    if (!uploadedImageFile) {
      toast({
        title: "❌ Error",
        description: "No hay imagen para procesar",
        variant: "destructive",
      });
      return;
    }

    // Validar prompt para modos que lo requieren
    if ((mode === "conservative" || mode === "creative") && !prompt.trim()) {
      toast({
        title: "❌ Prompt requerido",
        description: `El modo ${mode === "conservative" ? "Conservador" : "Creativo"} requiere una descripción`,
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setProgressMessage("Iniciando mejora de calidad...");
    setResultImage(null);

    try {
      const options: UpscaleOptions = {
        image: uploadedImageFile,
        mode,
        prompt: prompt.trim() || undefined,
        negativePrompt: negativePrompt.trim() || undefined,
        seed: seed && seed > 0 ? seed : undefined,
        outputFormat,
        creativity: creativity[0],
        stylePreset: stylePreset || undefined,
      };

      const resultUrl = await upscaleImageWithPolling(
        options,
        (progressValue, message) => {
          setProgress(progressValue);
          setProgressMessage(message || "Procesando...");
        }
      );

      setResultImage(resultUrl);
      onResult(resultUrl);

      toast({
        title: "✅ ¡Imagen mejorada!",
        description: `Calidad mejorada exitosamente con modo ${currentModeInfo?.name || mode}`,
      });

    } catch (error) {
      console.error("Error en upscale:", error);
      toast({
        title: "❌ Error al mejorar",
        description: error instanceof Error ? error.message : "Error desconocido",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProgress(0);
      setProgressMessage("");
    }
  };

  // Manejar descarga
  const handleDownload = () => {
    if (!resultImage) return;

    const link = document.createElement("a");
    link.href = resultImage;
    link.download = `${originalFilename.replace(/\.[^/.]+$/, "")}_upscaled.${outputFormat}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "📥 Descarga iniciada",
      description: "La imagen mejorada se está descargando",
    });
  };

  // Manejar copia al portapapeles
  const handleCopyToClipboard = async () => {
    if (!resultImage) return;

    try {
      // Convertir data URL a blob
      const response = await fetch(resultImage);
      const blob = await response.blob();

      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);

      toast({
        title: "📋 ¡Copiada!",
        description: "Imagen copiada al portapapeles exitosamente",
      });
    } catch (error) {
      console.error("Error copiando al portapapeles:", error);
      toast({
        title: "❌ Error al copiar",
        description: "No se pudo copiar la imagen al portapapeles",
        variant: "destructive",
      });
    }
  };

  // Manejar guardado
  const handleSave = () => {
    if (!resultImage) return;
    onSave?.(resultImage);
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Panel izquierdo - Imagen y controles */}
          <div className="space-y-6">
            {/* Imagen original */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Imagen Original
                </CardTitle>
              </CardHeader>
              <CardContent>
                {uploadedImage ? (
                  <div className="space-y-4">
                    <div className="relative">
                      <img
                        src={uploadedImage}
                        alt="Imagen original"
                        className="w-full h-auto rounded-lg border"
                      />
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p><strong>Archivo:</strong> {originalFilename}</p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-muted-foreground">No hay imagen cargada</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Configuración */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Configuración
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Selector de modo */}
                <div className="space-y-2">
                  <Label>Modo de Mejora</Label>
                  <Select value={mode} onValueChange={(value: any) => setMode(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {modes.map((modeInfo) => (
                        <SelectItem key={modeInfo.id} value={modeInfo.id}>
                          <div className="flex items-center gap-2">
                            <span>{modeInfo.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {modeInfo.credits} créditos
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {currentModeInfo && (
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p>{currentModeInfo.description}</p>
                      <p><strong>Resolución máxima:</strong> {currentModeInfo.max_resolution}</p>
                      <p><strong>Tiempo estimado:</strong> {currentModeInfo.processing_time}</p>
                    </div>
                  )}
                </div>

                {/* Prompt (para modos conservative y creative) */}
                {(mode === "conservative" || mode === "creative") && (
                  <div className="space-y-2">
                    <Label>
                      Descripción de la imagen {mode === "conservative" || mode === "creative" ? "*" : ""}
                    </Label>
                    <Textarea
                      placeholder="Describe la imagen para obtener mejores resultados (ej: 'retrato de una persona', 'paisaje natural', 'edificio moderno')"
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      rows={3}
                    />
                  </div>
                )}

                {/* Prompt negativo (opcional) */}
                {(mode === "conservative" || mode === "creative") && (
                  <div className="space-y-2">
                    <Label>Prompt Negativo (opcional)</Label>
                    <Textarea
                      placeholder="Qué NO quieres ver en la imagen (ej: 'borroso, pixelado, artefactos')"
                      value={negativePrompt}
                      onChange={(e) => setNegativePrompt(e.target.value)}
                      rows={2}
                    />
                  </div>
                )}

                {/* Creatividad (para modo creative) */}
                {mode === "creative" && (
                  <div className="space-y-2">
                    <Label>Nivel de Creatividad: {creativity[0]}</Label>
                    <Slider
                      value={creativity}
                      onValueChange={setCreativity}
                      min={0.1}
                      max={0.5}
                      step={0.1}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      Valores más altos agregan más detalles durante la mejora
                    </div>
                  </div>
                )}

                {/* Estilo (para modo creative) */}
                {mode === "creative" && stylePresets.length > 0 && (
                  <div className="space-y-2">
                    <Label>Estilo (opcional)</Label>
                    <Select value={stylePreset} onValueChange={setStylePreset}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar estilo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Sin estilo específico</SelectItem>
                        {stylePresets.map((preset) => (
                          <SelectItem key={preset.id} value={preset.id}>
                            {preset.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Formato de salida */}
                <div className="space-y-2">
                  <Label>Formato de Salida</Label>
                  <Select value={outputFormat} onValueChange={(value: any) => setOutputFormat(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="webp">WebP (recomendado)</SelectItem>
                      <SelectItem value="png">PNG (sin pérdida)</SelectItem>
                      <SelectItem value="jpeg">JPEG (menor tamaño)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Seed (opcional) */}
                <div className="space-y-2">
                  <Label>Seed (opcional)</Label>
                  <Input
                    type="number"
                    placeholder="Dejar vacío para aleatorio"
                    value={seed || ""}
                    onChange={(e) => setSeed(e.target.value ? parseInt(e.target.value) : undefined)}
                    min={0}
                    max={4294967294}
                  />
                </div>

                {/* Botón de procesamiento */}
                <Button
                  onClick={handleProcess}
                  disabled={isProcessing || !uploadedImageFile}
                  className="w-full"
                  size="lg"
                >
                  {isProcessing ? (
                    <>
                      <Cpu className="w-4 h-4 mr-2 animate-spin" />
                      Mejorando Calidad...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Mejorar Calidad
                    </>
                  )}
                </Button>

                {/* Barra de progreso */}
                {isProcessing && (
                  <div className="space-y-2">
                    <Progress value={progress} className="w-full" />
                    <p className="text-sm text-muted-foreground text-center">
                      {progressMessage}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Panel derecho - Resultado */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Imagen Mejorada
                </CardTitle>
              </CardHeader>
              <CardContent>
                {resultImage ? (
                  <div className="space-y-4">
                    <div className="relative">
                      <img
                        src={resultImage}
                        alt="Imagen mejorada"
                        className="w-full h-auto rounded-lg border"
                      />
                    </div>

                    {/* Acciones */}
                    <div className="flex flex-wrap gap-2">
                      <Button onClick={handleDownload} variant="outline">
                        <Download className="w-4 h-4 mr-2" />
                        Descargar
                      </Button>
                      <Button onClick={handleCopyToClipboard} variant="outline">
                        <Copy className="w-4 h-4 mr-2" />
                        Copiar
                      </Button>
                      {onSave && (
                        <Button onClick={handleSave} variant="outline">
                          <Heart className="w-4 h-4 mr-2" />
                          Guardar
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Sparkles className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      La imagen mejorada aparecerá aquí
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Información adicional */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Información
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="space-y-2">
                  <h4 className="font-semibold">Modos disponibles:</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li><strong>Rápido:</strong> Mejora 4x en ~1 segundo (1 crédito)</li>
                    <li><strong>Conservador:</strong> Hasta 4K preservando aspectos (25 créditos)</li>
                    <li><strong>Creativo:</strong> Hasta 4K reimaginando la imagen (25 créditos)</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">Consejos:</h4>
                  <ul className="space-y-1 text-muted-foreground">
                    <li>• Usa modo Rápido para mejoras simples</li>
                    <li>• Usa modo Conservador para preservar detalles</li>
                    <li>• Usa modo Creativo para imágenes muy degradadas</li>
                    <li>• Describe bien la imagen para mejores resultados</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
}
