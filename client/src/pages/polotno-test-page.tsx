import React from "react";
import { PolotnoStudio } from "@/components/polotno/PolotnoStudio";

const PolotnoTestPage: React.FC = () => {
  const handlePreview = (dataUrl: string) => {
    console.log("Preview generated:", dataUrl);
    // You can display the preview or download it
    const link = document.createElement("a");
    link.download = "polotno-design.png";
    link.href = dataUrl;
    link.click();
  };

  const handleSave = (json: any) => {
    console.log("Design saved:", json);
    // You can save the JSON to your backend or localStorage
    localStorage.setItem("polotno-design", JSON.stringify(json));
    alert("Design saved to localStorage!");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-pink-600 bg-clip-text text-transparent mb-2">
            Emma Studio - Complete Polotno Implementation
          </h1>
          <p className="text-gray-600 text-lg">
            Full-featured design studio with all Polotno capabilities + Emma branding
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div style={{ height: "800px" }}>
            <PolotnoStudio
              width={1080}
              height={1080}
              initialText="Welcome to Emma Studio!"
              platform="instagram"
              onPreview={handlePreview}
              onSave={handleSave}
              className="w-full h-full"
            />
          </div>
        </div>

        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <h3 className="font-bold text-blue-900 mb-3 text-lg">🎨 Emma Features</h3>
            <ul className="text-blue-800 space-y-2 text-sm">
              <li>• <strong>Emma Section:</strong> Branded templates and colors</li>
              <li>• <strong>Professional Toolbar:</strong> Load, Save, Export options</li>
              <li>• <strong>Multiple Formats:</strong> PNG, JPG, PDF export</li>
              <li>• <strong>High Quality:</strong> 3x pixel ratio for crisp images</li>
              <li>• <strong>Brand Colors:</strong> Emma blue (#3018ef) and pink (#dd3a5a)</li>
            </ul>
          </div>

          <div className="p-6 bg-gradient-to-br from-pink-50 to-rose-50 rounded-lg border border-pink-200">
            <h3 className="font-bold text-pink-900 mb-3 text-lg">🚀 Full Polotno Features</h3>
            <ul className="text-pink-800 space-y-2 text-sm">
              <li>• <strong>Text Tools:</strong> Fonts, styles, effects</li>
              <li>• <strong>Elements:</strong> Shapes, icons, illustrations</li>
              <li>• <strong>Photos:</strong> Stock photos and uploads</li>
              <li>• <strong>Backgrounds:</strong> Colors, gradients, patterns</li>
              <li>• <strong>Effects:</strong> Filters, shadows, animations</li>
              <li>• <strong>Layers:</strong> Full layer management</li>
              <li>• <strong>Templates:</strong> Pre-made designs</li>
              <li>• <strong>AI Features:</strong> Smart suggestions</li>
            </ul>
          </div>
        </div>

        <div className="mt-6 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border">
          <h3 className="font-bold text-gray-900 mb-3 text-lg">📋 How to Use</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Getting Started</h4>
              <ul className="text-gray-700 space-y-1">
                <li>1. Click "Emma" tab for branded templates</li>
                <li>2. Choose a template or start blank</li>
                <li>3. Use side panels to add content</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Editing</h4>
              <ul className="text-gray-700 space-y-1">
                <li>1. Click elements to select them</li>
                <li>2. Use toolbar for formatting</li>
                <li>3. Drag to move, resize handles to scale</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Exporting</h4>
              <ul className="text-gray-700 space-y-1">
                <li>1. Click PNG/JPG/PDF to export</li>
                <li>2. Use "Save" to store project</li>
                <li>3. Use "Load" to open saved projects</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PolotnoTestPage;
