/**
 * Página para aplicar estilo de referencia
 * Permite aplicar el estilo de una imagen de referencia a una nueva generación
 */
import React from "react";
import { <PERSON> } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Palette } from "lucide-react";
import StyleReferenceGenerator from "@/components/style-reference/StyleReferenceGenerator";
import DashboardLayout from "@/components/layout/dashboard-layout";

export default function StyleReferencePage() {
  return (
    <DashboardLayout pageTitle="Aplicar Estilo de Referencia">
      <div className="container max-w-7xl mx-auto">
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-rose-500 to-pink-600 rounded-xl flex items-center justify-center">
                <Palette className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Aplicar Estilo de Referencia
                </h1>
                <p className="text-gray-600">
                  Aplica el estilo de una imagen de referencia a tu nueva creación con IA
                </p>
              </div>
            </div>
            
            <div className="flex gap-3">
              <Link href="/visual-tools">
                <Button variant="outline" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Regresar a Herramientas
                </Button>
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <StyleReferenceGenerator />
        </div>
      </div>
    </DashboardLayout>
  );
}
