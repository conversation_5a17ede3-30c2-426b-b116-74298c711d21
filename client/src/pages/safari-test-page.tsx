import React from "react";
import { useSafariNavigation } from "@/hooks/use-safari-navigation";
import { isS<PERSON><PERSON>, isMobileSafari, createSafariLinkHandler } from "@/utils/safari-fixes";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function SafariTestPage() {
  const { navigate, forceNavigate } = useSafariNavigation();

  const testMethods = [
    {
      name: "Hook Navigation",
      action: () => navigate("/login"),
      description: "Usa el hook useSafariNavigation"
    },
    {
      name: "Force Navigation", 
      action: () => forceNavigate("/login"),
      description: "Fuerza la navegación con window.location"
    },
    {
      name: "Direct Location",
      action: () => { window.location.href = "/login"; },
      description: "Navegación directa con window.location.href"
    },
    {
      name: "History API",
      action: () => {
        window.history.pushState({}, '', '/login');
        window.dispatchEvent(new PopStateEvent('popstate'));
      },
      description: "Usa History API + popstate event"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">
              🧪 Safari Navigation Test Page
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Browser Detection */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Detección del Navegador</h3>
                <div className="space-y-2">
                  <div className={`p-3 rounded-lg ${isSafari() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    <strong>Safari:</strong> {isSafari() ? '✅ Detectado' : '❌ No detectado'}
                  </div>
                  <div className={`p-3 rounded-lg ${isMobileSafari() ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                    <strong>Mobile Safari:</strong> {isMobileSafari() ? '✅ Detectado' : '❌ No detectado'}
                  </div>
                  <div className="p-3 rounded-lg bg-blue-100 text-blue-800">
                    <strong>User Agent:</strong> {navigator.userAgent}
                  </div>
                </div>
              </div>

              {/* Current Location */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Información de Navegación</h3>
                <div className="space-y-2">
                  <div className="p-3 rounded-lg bg-gray-100">
                    <strong>Pathname:</strong> {window.location.pathname}
                  </div>
                  <div className="p-3 rounded-lg bg-gray-100">
                    <strong>Hash:</strong> {window.location.hash || 'N/A'}
                  </div>
                  <div className="p-3 rounded-lg bg-gray-100">
                    <strong>Search:</strong> {window.location.search || 'N/A'}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Pruebas de Navegación a /login</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {testMethods.map((method, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <h4 className="font-semibold mb-2">{method.name}</h4>
                  <p className="text-sm text-gray-600 mb-3">{method.description}</p>
                  <Button 
                    onClick={method.action}
                    className="w-full"
                    variant={index === 0 ? "default" : "outline"}
                  >
                    Probar {method.name}
                  </Button>
                </div>
              ))}
            </div>

            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-semibold text-yellow-800 mb-2">📝 Instrucciones de Prueba</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-yellow-700">
                <li>Abre esta página en Safari</li>
                <li>Prueba cada método de navegación</li>
                <li>Observa cuál funciona mejor</li>
                <li>Revisa la consola del navegador para logs</li>
              </ol>
            </div>

            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">🔗 Enlaces de Prueba Adicionales</h4>
              <div className="space-y-2">
                <a 
                  href="/register" 
                  onClick={createSafariLinkHandler("/register")}
                  className="block p-2 bg-white border rounded hover:bg-gray-50 transition-colors"
                >
                  Ir a Registro (Safari Link Handler)
                </a>
                <a 
                  href="/dashboard" 
                  className="block p-2 bg-white border rounded hover:bg-gray-50 transition-colors"
                >
                  Ir a Dashboard (Enlace Normal)
                </a>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
