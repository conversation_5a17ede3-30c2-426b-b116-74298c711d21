import React, { useState } from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import {
  ArrowLeft,
  Palette,
  Sparkles,
  Target,
  Edit3,
  Download,
  Share2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import TemplateManager from "@/components/ads-central/template-manager";
import { useToast } from "@/hooks/use-toast";

interface AdTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  platform: string;
  width: number;
  height: number;
  thumbnail: string;
  fileUrl: string;
  tags: string[];
  isFavorite: boolean;
  createdAt: string;
  fileType: 'image' | 'svg' | 'json';
}

function AdsTemplatesContent() {
  const [, navigate] = useLocation();
  const { toast } = useToast();

  const handleSelectTemplate = (template: AdTemplate) => {
    // Navegar al editor profesional con la plantilla seleccionada
    const editorUrl = `/visual-editor?template=${template.id}&platform=${template.platform}&width=${template.width}&height=${template.height}`;
    navigate(editorUrl);
    
    toast({
      title: "Abriendo en Editor Profesional",
      description: `Cargando plantilla: ${template.name}`,
    });
  };

  const handleEditTemplate = (template: AdTemplate) => {
    // Abrir directamente en el editor con la plantilla
    handleSelectTemplate(template);
  };

  const handleDownloadTemplate = (template: AdTemplate) => {
    // Simular descarga de plantilla
    const link = document.createElement('a');
    link.href = template.fileUrl;
    link.download = `${template.name}.${template.fileType}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Descarga iniciada",
      description: `Descargando: ${template.name}`,
    });
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 p-8 text-white"
      >
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/dashboard/ads-central")}
              className="text-white hover:bg-white/20 p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
              <Palette className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold">Plantillas de Anuncios</h1>
          </div>
          <p className="text-xl text-purple-100 mb-6 max-w-3xl">
            Biblioteca completa de plantillas profesionales de Figma. 
            Selecciona, personaliza y crea anuncios impactantes en minutos.
          </p>
          <div className="flex flex-wrap gap-2">
            <Badge className="bg-white/20 text-white border-white/30">
              <Sparkles className="w-3 h-3 mr-1" />
              Plantillas Figma
            </Badge>
            <Badge className="bg-white/20 text-white border-white/30">
              <Edit3 className="w-3 h-3 mr-1" />
              Editor Profesional
            </Badge>
            <Badge className="bg-white/20 text-white border-white/30">
              <Target className="w-3 h-3 mr-1" />
              Multi-plataforma
            </Badge>
          </div>
        </div>
        
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full blur-2xl"></div>
      </motion.div>

      {/* Instrucciones rápidas */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-blue-500 rounded-lg">
              <Download className="h-5 w-5 text-white" />
            </div>
            <h3 className="font-semibold text-blue-900">1. Sube tus Plantillas</h3>
          </div>
          <p className="text-blue-700 text-sm">
            Exporta tus diseños de Figma como PNG, SVG o JSON y súbelos a tu biblioteca personal.
          </p>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-purple-500 rounded-lg">
              <Edit3 className="h-5 w-5 text-white" />
            </div>
            <h3 className="font-semibold text-purple-900">2. Personaliza</h3>
          </div>
          <p className="text-purple-700 text-sm">
            Selecciona una plantilla y ábrela en el Editor Profesional para personalizarla completamente.
          </p>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-green-500 rounded-lg">
              <Share2 className="h-5 w-5 text-white" />
            </div>
            <h3 className="font-semibold text-green-900">3. Exporta y Usa</h3>
          </div>
          <p className="text-green-700 text-sm">
            Exporta en el formato perfecto para cada plataforma y lanza tus campañas publicitarias.
          </p>
        </div>
      </motion.div>

      {/* Template Manager */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <TemplateManager
          onSelectTemplate={handleSelectTemplate}
          onEditTemplate={handleEditTemplate}
        />
      </motion.div>

      {/* Tips adicionales */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 rounded-xl border border-gray-200"
      >
        <h3 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-yellow-500" />
          Tips para mejores resultados
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
          <div>
            <strong>Formatos recomendados:</strong>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>PNG/JPG para plantillas de imagen</li>
              <li>SVG para gráficos vectoriales</li>
              <li>JSON para plantillas editables de Polotno</li>
            </ul>
          </div>
          <div>
            <strong>Organización:</strong>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Usa tags descriptivos para facilitar la búsqueda</li>
              <li>Organiza por plataforma y tipo de campaña</li>
              <li>Marca como favoritas las más utilizadas</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

function AdsTemplatesPage() {
  return (
    <DashboardLayout pageTitle="Plantillas de Anuncios">
      <AdsTemplatesContent />
    </DashboardLayout>
  );
}

export default AdsTemplatesPage;
