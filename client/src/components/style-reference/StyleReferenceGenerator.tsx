/**
 * Componente principal para aplicar estilo de referencia
 * Permite subir una imagen de referencia y generar nuevas imágenes con ese estilo
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useToast } from '@/hooks/use-toast';
import {
  applyStyleReference,
  StyleReferenceOptions,
} from '@/services/style-reference-service';
import {
  Upload,
  Image as ImageIcon,
  Wand2,
  Loader2,
  Download,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>rkles,
  Trash2,
} from 'lucide-react';
import { motion } from 'framer-motion';

// Interfaz para imágenes guardadas
interface SavedStyleReference {
  id: string;
  referenceUrl: string;
  processedUrl: string;
  referenceFilename: string;
  prompt: string;
  negativePrompt?: string;
  aspectRatio: string;
  fidelity: number;
  seed?: number;
  outputFormat: string;
  stylePreset?: string;
  timestamp: number;
  isFavorite: boolean;
}

// Custom hook para localStorage que funciona mejor en Safari
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  // Estado para almacenar nuestro valor
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Función para actualizar el estado y localStorage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Permitir que value sea una función para que tengamos la misma API que useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;

      // Guardar en estado
      setStoredValue(valueToStore);

      // Guardar en localStorage
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));

        // Disparar evento personalizado para sincronización entre componentes
        window.dispatchEvent(new CustomEvent('localStorage', {
          detail: { key, newValue: valueToStore }
        }));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  // Escuchar cambios en localStorage (incluyendo nuestro evento personalizado)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent | CustomEvent) => {
      if ('detail' in e) {
        // Nuestro evento personalizado
        if (e.detail.key === key) {
          setStoredValue(e.detail.newValue);
        }
      } else {
        // Evento nativo de storage
        if (e.key === key && e.newValue !== null) {
          try {
            setStoredValue(JSON.parse(e.newValue));
          } catch (error) {
            console.log(`Error parsing localStorage value for key "${key}":`, error);
          }
        }
      }
    };

    // Escuchar ambos tipos de eventos
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('localStorage', handleStorageChange as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('localStorage', handleStorageChange as EventListener);
    };
  }, [key]);

  return [storedValue, setValue];
};

// Funciones para manejar imágenes guardadas
const SAVED_STYLE_REFERENCES_KEY = 'emma_saved_style_references';

const createSavedStyleReference = (styleRefData: Omit<SavedStyleReference, 'id' | 'timestamp' | 'isFavorite'>): SavedStyleReference => {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 1000000).toString();

  return {
    ...styleRefData,
    id: `style_ref_${timestamp}_${randomPart}`,
    timestamp: timestamp,
    isFavorite: true,
  };
};

const isStyleReferenceSaved = (imageUrl: string, savedStyleReferences: SavedStyleReference[]): boolean => {
  return savedStyleReferences.some(styleRef => styleRef.processedUrl === imageUrl);
};

// Opciones de aspect ratio
const ASPECT_RATIOS = [
  { value: "1:1", label: "Cuadrado (1:1)", description: "Instagram posts" },
  { value: "16:9", label: "Horizontal (16:9)", description: "YouTube thumbnails" },
  { value: "9:16", label: "Vertical (9:16)", description: "Instagram Stories" },
  { value: "4:5", label: "Retrato (4:5)", description: "Instagram posts" },
  { value: "3:2", label: "Fotografía (3:2)", description: "Cámaras DSLR" },
  { value: "21:9", label: "Panorámico (21:9)", description: "Banners web" },
];

// Estilos preestablecidos
const STYLE_PRESETS = [
  { value: "photographic", label: "Fotográfico", description: "Realista y detallado" },
  { value: "digital-art", label: "Arte Digital", description: "Moderno y vibrante" },
  { value: "cinematic", label: "Cinematográfico", description: "Dramático y atmosférico" },
  { value: "anime", label: "Anime", description: "Estilo japonés" },
  { value: "comic-book", label: "Cómic", description: "Ilustración de cómic" },
  { value: "fantasy-art", label: "Arte Fantástico", description: "Mágico y épico" },
  { value: "3d-model", label: "Modelo 3D", description: "Renderizado 3D" },
  { value: "pixel-art", label: "Pixel Art", description: "Retro y pixelado" },
  { value: "line-art", label: "Arte Lineal", description: "Líneas y contornos" },
  { value: "analog-film", label: "Película Analógica", description: "Vintage y nostálgico" },
];

export default function StyleReferenceGenerator() {
  const [referenceImage, setReferenceImage] = useState<File | null>(null);
  const [referenceImagePreview, setReferenceImagePreview] = useState<string | null>(null);
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [aspectRatio, setAspectRatio] = useState('1:1');
  const [fidelity, setFidelity] = useState([0.5]);
  const [seed, setSeed] = useState(0);
  const [outputFormat, setOutputFormat] = useState('png');
  const [stylePreset, setStylePreset] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [currentImageSaved, setCurrentImageSaved] = useState(false);

  // Estados para la funcionalidad de guardados
  const [savedStyleReferences, setSavedStyleReferences] = useLocalStorage<SavedStyleReference[]>(SAVED_STYLE_REFERENCES_KEY, []);
  const [activeTab, setActiveTab] = useState("latest");

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Verificar si la imagen actual está guardada
  useEffect(() => {
    if (generatedImage) {
      setCurrentImageSaved(isStyleReferenceSaved(generatedImage, savedStyleReferences));
    }
  }, [generatedImage, savedStyleReferences]);

  // Manejar selección de imagen de referencia
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validar tipo de archivo
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Archivo inválido",
          description: "Por favor selecciona una imagen válida (JPEG, PNG, WebP)",
          variant: "destructive",
        });
        return;
      }

      // Validar tamaño (máximo 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "Archivo muy grande",
          description: "La imagen debe ser menor a 10MB",
          variant: "destructive",
        });
        return;
      }

      setReferenceImage(file);

      // Crear preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setReferenceImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      toast({
        title: "Imagen cargada",
        description: `${file.name} (${Math.round(file.size / 1024)} KB)`,
      });
    }
  };

  // Manejar generación
  const handleGenerate = async () => {
    if (!referenceImage) {
      toast({
        title: "Imagen requerida",
        description: "Por favor selecciona una imagen de referencia",
        variant: "destructive",
      });
      return;
    }

    if (!prompt.trim()) {
      toast({
        title: "Prompt requerido",
        description: "Por favor describe qué quieres generar",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsGenerating(true);

      const options: StyleReferenceOptions = {
        image: referenceImage,
        prompt: prompt.trim(),
        negativePrompt: negativePrompt.trim() || undefined,
        aspectRatio: aspectRatio as any,
        fidelity: fidelity[0],
        seed: seed === 0 ? undefined : seed,
        outputFormat: outputFormat as any,
        stylePreset: stylePreset || undefined,
      };

      console.log("🎨 Iniciando aplicación de estilo de referencia:", options);

      const imageUrl = await applyStyleReference(options);

      setGeneratedImage(imageUrl);
      setCurrentImageSaved(false); // Reset saved status for new image

      toast({
        title: "¡Estilo aplicado exitosamente!",
        description: "Tu nueva imagen está lista",
      });

    } catch (error) {
      console.error("Error aplicando estilo de referencia:", error);
      toast({
        title: "Error en la generación",
        description: error instanceof Error ? error.message : "Error desconocido",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Generar seed aleatorio
  const generateRandomSeed = () => {
    setSeed(Math.floor(Math.random() * 4294967294));
  };

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!generatedImage || !referenceImagePreview) return;

    try {
      if (currentImageSaved) {
        // Quitar de favoritos
        const savedStyleRef = savedStyleReferences.find(styleRef => styleRef.processedUrl === generatedImage);
        if (savedStyleRef) {
          const filteredStyleReferences = savedStyleReferences.filter(styleRef => styleRef.id !== savedStyleRef.id);
          setSavedStyleReferences(filteredStyleReferences);
          setCurrentImageSaved(false);

          toast({
            title: "💔 Eliminada de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const styleRefData = {
          referenceUrl: referenceImagePreview,
          processedUrl: generatedImage,
          referenceFilename: referenceImage?.name || "imagen-referencia",
          prompt: prompt,
          negativePrompt: negativePrompt || undefined,
          aspectRatio: aspectRatio,
          fidelity: fidelity[0],
          seed: seed === 0 ? undefined : seed,
          outputFormat: outputFormat,
          stylePreset: stylePreset || undefined,
        };

        const newStyleReference = createSavedStyleReference(styleRefData);
        const updatedStyleReferences = [newStyleReference, ...savedStyleReferences].slice(0, 50); // Limitar a 50

        setSavedStyleReferences(updatedStyleReferences);
        setCurrentImageSaved(true);

        toast({
          title: "❤️ ¡Guardada en favoritos!",
          description: "Imagen guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo guardar la imagen. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [generatedImage, referenceImagePreview, referenceImage?.name, prompt, negativePrompt, aspectRatio, fidelity, seed, outputFormat, stylePreset, currentImageSaved, savedStyleReferences, setSavedStyleReferences, toast]);

  // Copiar imagen al portapapeles
  const copyToClipboard = async () => {
    if (!generatedImage) return;

    try {
      const response = await fetch(generatedImage);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);
      
      toast({
        title: "¡Copiado!",
        description: "Imagen copiada al portapapeles",
      });
    } catch (error) {
      toast({
        title: "Error al copiar",
        description: "No se pudo copiar la imagen",
        variant: "destructive",
      });
    }
  };

  // Descargar imagen
  const downloadImage = () => {
    if (!generatedImage) return;

    const link = document.createElement('a');
    link.href = generatedImage;
    link.download = `style-reference-${Date.now()}.${outputFormat}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Descarga iniciada",
      description: "La imagen se está descargando",
    });
  };

  return (
    <div className="w-full">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="latest">Última Generación</TabsTrigger>
          <TabsTrigger value="saved">
            Guardados ({savedStyleReferences.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="latest" className="mt-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Panel de configuración */}
            <div className="space-y-6">
              {/* Imagen de referencia */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon className="w-5 h-5" />
                    Imagen de Referencia
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    {referenceImagePreview ? (
                      <div className="space-y-3">
                        <img
                          src={referenceImagePreview}
                          alt="Imagen de referencia"
                          className="max-w-full max-h-48 mx-auto rounded-lg object-contain"
                        />
                        <p className="text-sm text-gray-600">
                          {referenceImage?.name} ({Math.round((referenceImage?.size || 0) / 1024)} KB)
                        </p>
                        <Button variant="outline" size="sm">
                          Cambiar imagen
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                        <div>
                          <p className="text-lg font-medium text-gray-700">
                            Sube tu imagen de referencia
                          </p>
                          <p className="text-sm text-gray-500">
                            JPEG, PNG o WebP (máx. 10MB)
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageSelect}
                    className="hidden"
                  />
                </CardContent>
              </Card>

              {/* Prompt */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wand2 className="w-5 h-5" />
                    Descripción
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="prompt">¿Qué quieres generar?</Label>
                    <Textarea
                      id="prompt"
                      placeholder="Ej: Un retrato de una mujer elegante en un jardín de flores"
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="min-h-[100px] mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="negative-prompt">Prompt negativo (opcional)</Label>
                    <Textarea
                      id="negative-prompt"
                      placeholder="Ej: borroso, de baja calidad, distorsionado"
                      value={negativePrompt}
                      onChange={(e) => setNegativePrompt(e.target.value)}
                      className="min-h-[80px] mt-2"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Botón de generar */}
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !referenceImage || !prompt.trim()}
                className="w-full h-12 text-lg"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Aplicando estilo...
                  </>
                ) : (
                  <>
                    <Palette className="w-5 h-5 mr-2" />
                    Aplicar Estilo de Referencia
                  </>
                )}
              </Button>
            </div>

            {/* Panel de resultado */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="w-5 h-5" />
                    Resultado
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {generatedImage ? (
                    <div className="space-y-4">
                      <div className="relative group">
                        <img
                          src={generatedImage}
                          alt="Imagen generada"
                          className="w-full rounded-lg shadow-lg"
                        />
                      </div>

                      <div className="flex gap-2 justify-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={copyToClipboard}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copiar
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleToggleFavorite}
                          className={currentImageSaved ? "text-red-500 border-red-200 bg-red-50" : ""}
                        >
                          <Heart className={`h-4 w-4 mr-2 ${currentImageSaved ? "fill-current" : ""}`} />
                          {currentImageSaved ? "Guardada" : "Guardar"}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={downloadImage}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Descargar
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="aspect-square bg-gray-50 rounded-lg flex items-center justify-center">
                      <div className="text-center space-y-3">
                        <Palette className="w-16 h-16 text-gray-300 mx-auto" />
                        <p className="text-gray-500">
                          Tu imagen generada aparecerá aquí
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="saved" className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5 text-primary" />
                Estilos de Referencia Guardados
                <Badge variant="secondary">{savedStyleReferences.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {savedStyleReferences.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {savedStyleReferences.map((savedStyleRef) => (
                    <div
                      key={savedStyleRef.id}
                      className="bg-white border-2 border-gray-200 rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                    >
                      <div className="relative">
                        <img
                          src={savedStyleRef.processedUrl}
                          alt="Imagen guardada"
                          className="w-full h-48 object-cover"
                        />
                        <div className="absolute top-2 right-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const filteredStyleReferences = savedStyleReferences.filter(styleRef => styleRef.id !== savedStyleRef.id);
                              setSavedStyleReferences(filteredStyleReferences);

                              toast({
                                title: "💔 Eliminada",
                                description: "Imagen eliminada de favoritos.",
                              });
                            }}
                            className="bg-white/90 hover:bg-white"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="p-4">
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          <strong>Prompt:</strong> {savedStyleRef.prompt}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>Fidelidad: {savedStyleRef.fidelity}</span>
                          <span>{savedStyleRef.aspectRatio}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No hay imágenes guardadas
                  </h3>
                  <p className="text-gray-500">
                    Las imágenes que guardes aparecerán aquí para acceder fácilmente en el futuro.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
