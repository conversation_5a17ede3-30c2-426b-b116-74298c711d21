import { ImageDimensions } from '../types';

export const getplatformFromContentType = (contentType: string): string => {
  const platformMap: { [key: string]: string } = {
    "instagram_posts": "Instagram",
    "instagram_stories": "Instagram",
    "facebook_posts": "Facebook",
    "linkedin_posts": "LinkedIn",
    "twitter_posts": "X (Twitter)"
  };
  return platformMap[contentType] || "Instagram";
};

export const getImageDisplayDimensions = (platform: string, dimensions?: string): ImageDimensions => {
  console.log("🔍 PostGeneratorResults - Calculating dimensions for platform:", platform, "with metadata dimensions:", dimensions);

  // Define platform-specific display dimensions (optimized for tight container fitting)
  const platformDimensions: { [key: string]: ImageDimensions } = {
    instagram: { width: 380, height: 380, aspectRatio: 1 }, // Square - tighter fit
    facebook: { width: 420, height: 221, aspectRatio: 1.9 }, // 1200x630 ratio - more compact
    linkedin: { width: 420, height: 220, aspectRatio: 1.91 }, // 1200x627 ratio - more compact
    x: { width: 420, height: 236, aspectRatio: 1.78 }, // 1200x675 ratio - more compact
    twitter: { width: 420, height: 236, aspectRatio: 1.78 }, // Same as X
    "instagram stories": { width: 280, height: 497, aspectRatio: 0.56 } // Vertical stories - much taller
  };

  // Get platform-specific dimensions
  const platformKey = platform.toLowerCase();
  let result = platformDimensions[platformKey] || platformDimensions.instagram;

  // If we have actual dimensions from metadata, calculate based on those
  if (dimensions && dimensions.includes('x')) {
    try {
      const [width, height] = dimensions.split('x').map(Number);
      if (width && height) {
        const aspectRatio = width / height;

        // Dynamic scaling based on aspect ratio for better container fitting
        let displayWidth, displayHeight;

        if (aspectRatio > 1.5) {
          // Wide landscape (Facebook, LinkedIn, X/Twitter)
          displayWidth = 420;
          displayHeight = Math.round(displayWidth / aspectRatio);
        } else if (aspectRatio < 0.8) {
          // Portrait (Instagram Stories)
          displayHeight = 500;
          displayWidth = Math.round(displayHeight * aspectRatio);
        } else {
          // Square or near-square (Instagram posts)
          displayWidth = 380;
          displayHeight = Math.round(displayWidth / aspectRatio);
        }

        result = {
          width: displayWidth,
          height: displayHeight,
          aspectRatio: aspectRatio
        };
      }
    } catch (error) {
      console.warn("⚠️ PostGeneratorResults - Error parsing dimensions:", dimensions, error);
    }
  }

  console.log("📐 PostGeneratorResults - Calculated display dimensions:", result);
  return result;
};
