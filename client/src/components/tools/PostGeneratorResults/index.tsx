import React, { useEffect } from "react";
import { PostGeneratorResultsProps } from "./types";
import { usePostGeneration, useLoadingProgress } from "./hooks";
import { LoadingScreen, Header, PostsList } from "./components";

const PostGeneratorResults: React.FC<PostGeneratorResultsProps> = ({
  brandData,
  selectedTheme,
  selectedContentType,
  onBack,
}) => {
  const { isLoading, posts, error, generatePosts } = usePostGeneration();
  const { loadingStage, loadingProgress, startTime, setCompletionProgress } = useLoadingProgress(isLoading);

  useEffect(() => {
    generatePosts(brandData, selectedTheme, selectedContentType, false, setCompletionProgress); // Initial load
  }, [generatePosts, brandData, selectedTheme, selectedContentType, setCompletionProgress]);

  const handleGenerateMore = () => {
    generatePosts(brandData, selectedTheme, selectedContentType, true, setCompletionProgress);
  };

  const handleRetry = () => {
    generatePosts(brandData, selectedTheme, selectedContentType, false, setCompletionProgress);
  };

  const handlePreview = () => {
    // TODO: Implement preview functionality
    console.log("Preview posts");
  };

  if (isLoading) {
    return (
      <LoadingScreen
        brandData={brandData}
        loadingStage={loadingStage}
        loadingProgress={loadingProgress}
        startTime={startTime}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50">
      <Header
        selectedTheme={selectedTheme}
        selectedContentType={selectedContentType}
        onBack={onBack}
        onGenerateMore={handleGenerateMore}
        onPreview={handlePreview}
      />
      
      <PostsList
        posts={posts}
        brandData={brandData}
        onGenerateMore={handleGenerateMore}
        error={error}
        onRetry={handleRetry}
      />
    </div>
  );
};

export default PostGeneratorResults;
