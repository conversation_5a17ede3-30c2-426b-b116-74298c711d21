import React from "react";
import { motion } from "framer-motion";
import CreativeLoader from "@/components/ui/CreativeLoader";
import { LoadingScreenProps } from "../types";

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  brandData,
  loadingStage,
  loadingProgress,
  startTime
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 flex flex-col items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center"
      >
        {/* Creative Loader Animation */}
        <div className="mb-12">
          <CreativeLoader />
        </div>

        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-6xl font-bold mb-6"
          style={{
            background: 'linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}
        >
          El Marketing Ya Cambió
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed"
        >
          Emma está creando contenido único y personalizado para <span className="font-semibold text-purple-600">{brandData.businessName}</span> usando inteligencia artificial avanzada
        </motion.p>

        {/* Progress Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="w-full max-w-md mx-auto mb-6"
        >
          <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${loadingProgress}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-2">
            <span>{Math.round(loadingProgress)}%</span>
            <span>{Math.round((Date.now() - startTime) / 1000)}s</span>
          </div>
        </motion.div>

        {/* Current Stage */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mb-8"
        >
          <p className="text-lg font-medium text-gray-700 mb-2">{loadingStage}</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
          className="flex justify-center space-x-8 text-sm text-gray-500"
        >
          <div className="flex items-center">
            <motion.div
              animate={{
                scale: loadingProgress > 10 ? [1, 1.2, 1] : 1,
                backgroundColor: loadingProgress > 10 ? "#10b981" : "#6b7280"
              }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-3 h-3 rounded-full mr-3"
            ></motion.div>
            <span className={`font-medium ${loadingProgress > 10 ? 'text-green-600' : 'text-gray-500'}`}>
              Analizando tu marca
            </span>
          </div>
          <div className="flex items-center">
            <motion.div
              animate={{
                scale: loadingProgress > 30 ? [1, 1.2, 1] : 1,
                backgroundColor: loadingProgress > 30 ? "#3b82f6" : "#6b7280"
              }}
              transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
              className="w-3 h-3 rounded-full mr-3"
            ></motion.div>
            <span className={`font-medium ${loadingProgress > 30 ? 'text-blue-600' : 'text-gray-500'}`}>
              Generando contenido
            </span>
          </div>
          <div className="flex items-center">
            <motion.div
              animate={{
                scale: loadingProgress > 60 ? [1, 1.2, 1] : 1,
                backgroundColor: loadingProgress > 60 ? "#8b5cf6" : "#6b7280"
              }}
              transition={{ duration: 2, repeat: Infinity, delay: 1 }}
              className="w-3 h-3 rounded-full mr-3"
            ></motion.div>
            <span className={`font-medium ${loadingProgress > 60 ? 'text-purple-600' : 'text-gray-500'}`}>
              Creando imágenes
            </span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-12 text-center"
        >
          <div className="text-sm text-gray-400 mb-2">
            ⏱️ Generando contenido de máxima calidad
          </div>
          <div className="text-xs text-gray-400">
            Este proceso puede tomar hasta 5 minutos para crear contenido personalizado
          </div>
          {loadingProgress > 80 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-xs text-green-600 mt-2 font-medium"
            >
              🎉 ¡Casi terminado! Preparando tus posts...
            </motion.div>
          )}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default LoadingScreen;
