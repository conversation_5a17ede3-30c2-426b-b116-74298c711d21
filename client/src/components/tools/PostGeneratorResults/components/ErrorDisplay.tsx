import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ErrorDisplayProps } from "../types";

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, onRetry }) => {
  return (
    <div className="text-center py-12">
      <div className="text-red-500 mb-4">❌ {error}</div>
      <Button onClick={onRetry} variant="outline">
        Intentar de nuevo
      </Button>
    </div>
  );
};

export default ErrorDisplay;
