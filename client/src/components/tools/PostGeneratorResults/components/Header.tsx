import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { HeaderProps } from "../types";
import { getplatformFromContentType } from "../utils/platformUtils";

const Header: React.FC<HeaderProps> = ({
  selectedTheme,
  selectedContentType,
  onBack,
  onGenerateMore,
  onPreview
}) => {
  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              onClick={onBack}
              variant="outline"
              size="sm"
              className="flex items-center"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Posts Generados</h1>
              <p className="text-gray-600">Template: {selectedTheme} • {getplatformFromContentType(selectedContentType)}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              onClick={onGenerateMore}
              variant="outline"
              size="sm"
            >
              Generar más
            </Button>
            <Button
              onClick={onPreview}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white"
              size="sm"
            >
              <Eye className="w-4 h-4 mr-2" />
              Vista previa
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
