import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, Save, Download, Palette, Type, Image as ImageIcon, Layers } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { GeneratedPost, BrandData } from "../PostGenerator";

interface PostEditorProps {
  post: GeneratedPost;
  brandData: BrandData;
  onSave: (post: GeneratedPost) => void;
  onCancel: () => void;
}

const PostEditor: React.FC<PostEditorProps> = ({
  post,
  brandData,
  onSave,
  onCancel,
}) => {
  const [editedPost, setEditedPost] = useState<GeneratedPost>(post);
  const [activeTab, setActiveTab] = useState<'content' | 'design'>('content');
  const [isPolotnoLoaded, setIsPolotnoLoaded] = useState(false);
  const [polotnoStore, setPolotnoStore] = useState<any>(null);
  const polotnoContainerRef = useRef<HTMLDivElement>(null);

  // Initialize Polotno when design tab is active
  useEffect(() => {
    if (activeTab === 'design' && !isPolotnoLoaded && polotnoContainerRef.current) {
      initializePolotno();
    }
  }, [activeTab, isPolotnoLoaded]);

  const initializePolotno = async () => {
    try {
      // Dynamically import Polotno to avoid SSR issues
      const { PolotnoContainer, SidePanelWrap, WorkspaceWrap } = await import('polotno');
      const { Workspace } = await import('polotno/canvas/workspace');
      const { SidePanel } = await import('polotno/side-panel');
      const { Toolbar } = await import('polotno/toolbar/toolbar');
      const { ZoomButtons } = await import('polotno/toolbar/zoom-buttons');
      const { createStore } = await import('polotno/model/store');

      if (!polotnoContainerRef.current) return;

      // Create Polotno store
      const store = createStore({
        width: 1080,
        height: 1080,
        unit: 'px',
      });

      // Store reference for later use
      setPolotnoStore(store);

      // Add initial page
      const page = store.addPage();

      // If post has an existing image, try to load it as background
      if (editedPost.image_url) {
        try {
          page.addElement({
            type: 'image',
            x: 0,
            y: 0,
            width: 1080,
            height: 1080,
            src: editedPost.image_url,
          });
        } catch (imgError) {
          console.warn('Could not load existing image, creating new design:', imgError);
          // Fallback to brand color background
          page.addElement({
            type: 'rect',
            x: 0,
            y: 0,
            width: 1080,
            height: 1080,
            fill: brandData.brandColor,
          });
        }
      } else {
        // Add background with brand color
        page.addElement({
          type: 'rect',
          x: 0,
          y: 0,
          width: 1080,
          height: 1080,
          fill: brandData.brandColor,
        });
      }

      // Add business name text overlay with stroke for readability
      page.addElement({
        type: 'text',
        x: 50,
        y: 50,
        width: 980,
        height: 100,
        text: brandData.businessName,
        fontSize: 60,
        fontFamily: 'Arial',
        fill: '#ffffff',
        fontWeight: 'bold',
        align: 'center',
        stroke: '#000000',
        strokeWidth: 2,
      });

      // Add post content text overlay with stroke for readability
      page.addElement({
        type: 'text',
        x: 50,
        y: 200,
        width: 980,
        height: 600,
        text: editedPost.content,
        fontSize: 24,
        fontFamily: 'Arial',
        fill: '#ffffff',
        align: 'left',
        stroke: '#000000',
        strokeWidth: 1,
      });

      // Add CTA button
      if (editedPost.cta) {
        page.addElement({
          type: 'rect',
          x: 50,
          y: 900,
          width: 300,
          height: 80,
          fill: '#ffffff',
          cornerRadius: 40,
        });

        page.addElement({
          type: 'text',
          x: 50,
          y: 920,
          width: 300,
          height: 40,
          text: editedPost.cta,
          fontSize: 18,
          fontFamily: 'Arial',
          fill: brandData.brandColor,
          fontWeight: 'bold',
          align: 'center',
        });
      }

      setIsPolotnoLoaded(true);
    } catch (error) {
      console.error('Error loading Polotno:', error);
    }
  };

  const handleContentChange = (field: keyof GeneratedPost, value: any) => {
    setEditedPost(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleHashtagsChange = (value: string) => {
    const hashtags = value.split(',').map(tag => tag.trim().replace('#', '')).filter(Boolean);
    setEditedPost(prev => ({
      ...prev,
      hashtags
    }));
  };

  const handleSave = async () => {
    // If Polotno is loaded and we're on design tab, export the image
    if (polotnoStore && activeTab === 'design') {
      try {
        const dataURL = await polotnoStore.toDataURL();
        const updatedPost = {
          ...editedPost,
          image_url: dataURL
        };
        onSave(updatedPost);
      } catch (error) {
        console.error('Error exporting Polotno design:', error);
        onSave(editedPost);
      }
    } else {
      onSave(editedPost);
    }
  };

  const handleExport = async () => {
    if (polotnoStore) {
      try {
        // Export as high-quality image
        const dataURL = await polotnoStore.toDataURL({
          mimeType: 'image/png',
          quality: 1,
          pixelRatio: 2 // Higher resolution
        });

        // Create download link
        const link = document.createElement('a');
        link.download = `${brandData.businessName}_post_${Date.now()}.png`;
        link.href = dataURL;
        link.click();

        console.log('Post exported successfully');
      } catch (error) {
        console.error('Error exporting post:', error);
      }
    } else {
      console.log('Exporting text-only post:', editedPost);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={onCancel}
                variant="outline"
                size="sm"
                className="flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver
              </Button>
              <h1 className="text-xl font-bold text-gray-900">
                Editar Post - {brandData.businessName}
              </h1>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleExport}
                variant="outline"
                className="flex items-center"
              >
                <Download className="w-4 h-4 mr-2" />
                Exportar
              </Button>
              <Button
                onClick={handleSave}
                className="bg-gradient-to-r from-[#3018ef] to-[#4c51bf] hover:from-[#2516d6] hover:to-[#3d4db7] text-white flex items-center"
              >
                <Save className="w-4 h-4 mr-2" />
                Guardar Cambios
              </Button>
            </div>
          </div>
          
          {/* Tabs */}
          <div className="flex space-x-1 mt-4">
            <button
              onClick={() => setActiveTab('content')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'content'
                  ? 'bg-[#3018ef] text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Type className="w-4 h-4 inline mr-2" />
              Editar Texto
            </button>
            <button
              onClick={() => setActiveTab('design')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'design'
                  ? 'bg-[#3018ef] text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <ImageIcon className="w-4 h-4 inline mr-2" />
              Editar Imagen
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {activeTab === 'content' ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {/* Editor Panel */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Editar Contenido
                </h3>
                
                <div className="space-y-4">
                  {/* Post Content */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contenido del Post
                    </label>
                    <textarea
                      value={editedPost.content}
                      onChange={(e) => handleContentChange('content', e.target.value)}
                      rows={8}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent resize-none"
                      placeholder="Escribe el contenido de tu post..."
                    />
                  </div>

                  {/* Hashtags */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Hashtags (separados por comas)
                    </label>
                    <Input
                      value={editedPost.hashtags.join(', ')}
                      onChange={(e) => handleHashtagsChange(e.target.value)}
                      placeholder="marketing, digital, emma"
                    />
                  </div>

                  {/* CTA */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Call to Action
                    </label>
                    <Input
                      value={editedPost.cta}
                      onChange={(e) => handleContentChange('cta', e.target.value)}
                      placeholder="¡Contáctanos hoy!"
                    />
                  </div>

                  {/* Platform */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Plataforma
                    </label>
                    <select
                      value={editedPost.platform}
                      onChange={(e) => handleContentChange('platform', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent"
                    >
                      <option value="instagram">Instagram</option>
                      <option value="linkedin">LinkedIn</option>
                      <option value="facebook">Facebook</option>
                      <option value="twitter">X (Twitter)</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Preview Panel */}
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Vista Previa
                </h3>
                
                <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold"
                      style={{ backgroundColor: brandData.brandColor }}
                    >
                      {brandData.businessName.charAt(0)}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{brandData.businessName}</p>
                      <p className="text-sm text-gray-500 capitalize">{editedPost.platform}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <p className="text-gray-800 whitespace-pre-wrap">
                      {editedPost.content}
                    </p>
                    
                    {editedPost.hashtags.length > 0 && (
                      <p className="text-blue-600 text-sm">
                        {editedPost.hashtags.map(tag => `#${tag}`).join(' ')}
                      </p>
                    )}
                    
                    {editedPost.cta && (
                      <div 
                        className="inline-block px-4 py-2 rounded-lg text-white font-medium text-sm"
                        style={{ backgroundColor: brandData.brandColor }}
                      >
                        {editedPost.cta}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Layers className="w-5 h-5 mr-2" />
                Editor Visual con Polotno
              </h3>
              <div className="text-sm text-gray-500">
                Arrastra elementos, edita texto y personaliza tu diseño
              </div>
            </div>
            
            <div 
              ref={polotnoContainerRef}
              className="w-full h-[600px] border border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center"
            >
              {!isPolotnoLoaded ? (
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#3018ef] mx-auto mb-4"></div>
                  <p className="text-gray-600">Cargando editor visual...</p>
                </div>
              ) : (
                <div className="w-full h-full">
                  {/* Polotno will be rendered here */}
                  <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">Editor visual de Polotno</p>
                      <p className="text-sm text-gray-500 mt-2">
                        Funcionalidad completa disponible próximamente
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default PostEditor;
