import React, { useState } from "react";
import Step1BrandInput from "./ProfessionalPostGenerator/Step1BrandInput";
import Step2BusinessDetails from "./ProfessionalPostGenerator/Step2BusinessDetails";
import Step3ContentSettings from "./ProfessionalPostGenerator/Step3ContentSettings";
import Step4DesignSelection from "./ProfessionalPostGenerator/Step4DesignSelection";
import PostGenerator from "./PostGenerator/PostGenerator";
import { useBrandData } from "./ProfessionalPostGenerator/hooks/useBrandData";

const ProfessionalPostGenerator: React.FC = () => {
  const [step, setStep] = useState(1);
  const [showPostGenerator, setShowPostGenerator] = useState(false);
  const brandData = useBrandData();

  // Debug: Log current state and data flow
  React.useEffect(() => {
    console.log(`📍 Current step: ${step}`);
    console.log("📊 Brand data state:", {
      businessName: brandData.businessName,
      brandColor: brandData.brandColor,
      voice: brandData.voice ? brandData.voice.substring(0, 50) + '...' : 'Not set',
      topics: brandData.topics.length,
      ctas: brandData.ctas.length,
      analysisComplete: brandData.analysisComplete,
      hasAnalysis: !!brandData.brandAnalysis,
      brandUrl: brandData.brandUrl,
      brandDescription: brandData.brandDescription ? brandData.brandDescription.substring(0, 50) + '...' : 'Not set'
    });

    // Log specific data for each step to verify data flow
    if (step === 2) {
      console.log("🔍 Step 2 - Verifying data received:", {
        businessName: brandData.businessName,
        brandColor: brandData.brandColor,
        voice: brandData.voice
      });
    } else if (step === 3) {
      console.log("🔍 Step 3 - Verifying data received:", {
        topics: brandData.topics,
        ctas: brandData.ctas,
        businessName: brandData.businessName
      });
    } else if (step === 4) {
      console.log("🔍 Step 4 - Verifying data received:", {
        selectedTheme: brandData.selectedTheme,
        allData: {
          businessName: brandData.businessName,
          brandColor: brandData.brandColor,
          voice: brandData.voice,
          topics: brandData.topics,
          ctas: brandData.ctas
        }
      });
    }
  }, [step, brandData]);

  const handleContinueStep1 = async () => {
    // Validate that user has provided either URL or description
    if (!brandData.brandUrl.trim() && !brandData.brandDescription.trim()) {
      brandData.setAnalysisError("Por favor ingresa una URL o describe tu marca para continuar.");
      return;
    }

    // Clear any previous errors
    brandData.setAnalysisError("");

    // Try website analysis first if URL is provided
    if (brandData.brandUrl.trim()) {
      const success = await brandData.analyzeWebsite(brandData.brandUrl.trim());
      if (success) {
        console.log("✅ Website analysis successful, proceeding to step 2");
        setStep(2);
      } else {
        // Website analysis failed, but don't block progression
        console.log("⚠️ Website analysis failed, but allowing manual progression");
        // The error message is already set by analyzeWebsite
        // User can still proceed with manual description or continue anyway
      }
    } else if (brandData.brandDescription.trim()) {
      // Process manual description with AI analysis
      console.log("📝 Processing manual brand description with AI");
      const success = await brandData.processDescription(brandData.brandDescription.trim());
      if (success) {
        console.log("✅ Manual description analysis successful, proceeding to step 2");
        setStep(2);
      } else {
        console.log("⚠️ Manual description analysis failed, but allowing progression");
        setStep(2);
      }
    }
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const handleNext = () => {
    console.log(`🚀 Moving from step ${step} to step ${step + 1}`);

    if (step < 4) {
      setStep(step + 1);
    } else {
      // Final step - transition to post generation
      const finalData = {
        businessName: brandData.businessName,
        brandColor: brandData.brandColor,
        voice: brandData.voice,
        topics: brandData.topics,
        ctas: brandData.ctas,
        selectedTheme: brandData.selectedTheme,
        selectedContentType: brandData.selectedContentType,
        analysisComplete: brandData.analysisComplete,
        brandAnalysis: brandData.brandAnalysis
      };

      console.log("🎯 Final data collected for post generation:", finalData);
      console.log("🚀 Transitioning to Post Generator...");
      console.log("🔍 Current showPostGenerator state:", showPostGenerator);

      // Transition to the Post Generator
      setShowPostGenerator(true);
      console.log("✅ setShowPostGenerator(true) called");
    }
  };

  const handleBackFromPostGenerator = () => {
    setShowPostGenerator(false);
    setStep(4); // Return to step 4
  };

  const handleBrandDescriptionChange = (value: string) => {
    brandData.setBrandDescription(value);
    // Limpiar error cuando el usuario empiece a escribir
    if (brandData.analysisError) {
      brandData.setAnalysisError("");
    }
  };

  // Show PostGenerator if requested
  if (showPostGenerator) {
    return (
      <PostGenerator
        brandData={{
          brandUrl: brandData.brandUrl,
          brandDescription: brandData.brandDescription,
          businessName: brandData.businessName,
          brandColor: brandData.brandColor,
          voice: brandData.voice,
          topics: brandData.topics,
          ctas: brandData.ctas,
        }}
        selectedTheme={brandData.selectedTheme}
        selectedContentType={brandData.selectedContentType}
        brandAnalysis={brandData.brandAnalysis}
        analysisComplete={brandData.analysisComplete}
        onBack={handleBackFromPostGenerator}
      />
    );
  }

  // Render components based on step
  if (step === 1) {
    return (
      <Step1BrandInput
        brandUrl={brandData.brandUrl}
        brandDescription={brandData.brandDescription}
        isAnalyzing={brandData.isAnalyzing}
        analysisError={brandData.analysisError}
        onBrandUrlChange={brandData.setBrandUrl}
        onBrandDescriptionChange={handleBrandDescriptionChange}
        onContinue={handleContinueStep1}
      />
    );
  }

  if (step === 2) {
    return (
      <Step2BusinessDetails
        businessName={brandData.businessName}
        brandColor={brandData.brandColor}
        voice={brandData.voice}
        analysisComplete={brandData.analysisComplete}
        brandAnalysis={brandData.brandAnalysis}
        onBusinessNameChange={brandData.setBusinessName}
        onBrandColorChange={brandData.setBrandColor}
        onVoiceChange={brandData.setVoice}
        onBack={handleBack}
        onNext={handleNext}
      />
    );
  }

  if (step === 3) {
    return (
      <Step3ContentSettings
        topics={brandData.topics}
        ctas={brandData.ctas}
        analysisComplete={brandData.analysisComplete}
        brandAnalysis={brandData.brandAnalysis}
        onTopicChange={brandData.updateTopic}
        onAddTopic={brandData.addTopic}
        onCtaChange={brandData.updateCta}
        onAddCta={brandData.addCta}
        onRemoveCta={brandData.removeCta}
        onBack={handleBack}
        onNext={handleNext}
        brandData={{
          businessName: brandData.businessName,
          brandColor: brandData.brandColor,
          voice: brandData.voice,
        }}
      />
    );
  }

  if (step === 4) {
    return (
      <Step4DesignSelection
        selectedTheme={brandData.selectedTheme}
        analysisComplete={brandData.analysisComplete}
        brandAnalysis={brandData.brandAnalysis}
        onThemeSelect={brandData.setSelectedTheme}
        onContentTypeSelect={brandData.setSelectedContentType}
        onBack={handleBack}
        onNext={handleNext}
        brandData={{
          brandUrl: brandData.brandUrl,
          brandDescription: brandData.brandDescription,
          businessName: brandData.businessName,
          brandColor: brandData.brandColor,
          voice: brandData.voice,
          topics: brandData.topics,
          ctas: brandData.ctas,
        }}
      />
    );
  }

  return null;
};

export default ProfessionalPostGenerator;
