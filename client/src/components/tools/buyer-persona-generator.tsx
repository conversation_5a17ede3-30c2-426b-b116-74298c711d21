"use client";

/**
 * Buyer Persona Generator - Main Entry Point
 *
 * This component has been refactored to use a modular architecture
 * with proper separation of concerns, following best practices.
 */

// Import the refactored component
import BuyerPersonaGeneratorRefactored from './buyer-persona-generator/buyer-persona-generator-refactored';

export default function BuyerPersonaGenerator() {
  // Use the refactored component
  return <BuyerPersonaGeneratorRefactored />;
}