import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  Edit3, 
  Trash2, 
  Calendar, 
  Eye,
  MoreVertical,
  Search,
  Filter
} from "lucide-react"
import { useLocation } from "wouter"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface MoodBoard {
  id: string
  title: string
  description: string
  thumbnail: string
  createdAt: string
  updatedAt: string
  tags: string[]
  isPublic: boolean
}

// Mock data - después conectaremos con backend
const mockMoodBoards: MoodBoard[] = [
  {
    id: "1",
    title: "Campaña Verano 2024",
    description: "Mood board para la campaña de verano con colores vibrantes y energía tropical",
    thumbnail: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
    createdAt: "2024-01-15",
    updatedAt: "2024-01-20",
    tags: ["verano", "tropical", "vibrante"],
    isPublic: false
  },
  {
    id: "2", 
    title: "Branding Minimalista",
    description: "Inspiración para marca minimalista con tonos neutros y tipografía clean",
    thumbnail: "https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=400&h=300&fit=crop",
    createdAt: "2024-01-10",
    updatedAt: "2024-01-18",
    tags: ["minimalista", "neutro", "clean"],
    isPublic: true
  },
  {
    id: "3",
    title: "E-commerce Fashion",
    description: "Mood board para tienda online de moda con estética moderna y elegante",
    thumbnail: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop",
    createdAt: "2024-01-05",
    updatedAt: "2024-01-15",
    tags: ["fashion", "ecommerce", "elegante"],
    isPublic: false
  }
]

export default function MoodBoardList() {
  const [, setLocation] = useLocation()
  const [moodBoards, setMoodBoards] = useState<MoodBoard[]>(mockMoodBoards)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")

  const filteredMoodBoards = moodBoards.filter(board => {
    const matchesSearch = board.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         board.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    if (selectedFilter === "all") return matchesSearch
    if (selectedFilter === "public") return matchesSearch && board.isPublic
    if (selectedFilter === "private") return matchesSearch && !board.isPublic
    
    return matchesSearch
  })

  const handleCreateNew = () => {
    // Crear nuevo mood board y redirigir al editor
    const newId = Date.now().toString()
    setLocation(`/dashboard/herramientas/mood-board/editor/${newId}`)
  }

  const handleOpenBoard = (boardId: string) => {
    setLocation(`/dashboard/herramientas/mood-board/editor/${boardId}`)
  }

  const handleDeleteBoard = (boardId: string) => {
    setMoodBoards(prev => prev.filter(board => board.id !== boardId))
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 text-transparent bg-clip-text">
              Mis Mood Boards
            </h1>
            <p className="text-gray-600 mt-2 text-lg">
              Gestiona y crea tableros de inspiración visual para tus proyectos
            </p>
          </div>
          
          <Button
            onClick={handleCreateNew}
            size="lg"
            className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3"
          >
            <Plus className="w-5 h-5 mr-2" />
            Crear Nuevo
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Buscar mood boards..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="w-4 h-4" />
                Filtrar
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setSelectedFilter("all")}>
                Todos
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedFilter("public")}>
                Públicos
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedFilter("private")}>
                Privados
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </motion.div>

      {/* Mood Boards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMoodBoards.map((board, index) => (
          <motion.div
            key={board.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden">
              <div className="relative">
                <img
                  src={board.thumbnail}
                  alt={board.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleOpenBoard(board.id)}
                    className="bg-white text-gray-800 hover:bg-white/90"
                  >
                    <Edit3 className="w-4 h-4 mr-1" />
                    Editar
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-white/90 text-gray-800 hover:bg-white"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    Ver
                  </Button>
                </div>
                
                {/* Status Badge */}
                <div className="absolute top-3 right-3">
                  <Badge variant={board.isPublic ? "default" : "secondary"}>
                    {board.isPublic ? "Público" : "Privado"}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-lg line-clamp-1">{board.title}</h3>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => handleOpenBoard(board.id)}>
                        <Edit3 className="w-4 h-4 mr-2" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteBoard(board.id)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Eliminar
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{board.description}</p>
                
                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {board.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                {/* Date */}
                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="w-3 h-3 mr-1" />
                  Actualizado {formatDate(board.updatedAt)}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {filteredMoodBoards.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Plus className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No hay mood boards</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm ? "No se encontraron resultados para tu búsqueda" : "Crea tu primer mood board para empezar"}
          </p>
          <Button onClick={handleCreateNew} className="bg-gradient-to-r from-indigo-600 to-purple-600">
            <Plus className="w-4 h-4 mr-2" />
            Crear Mood Board
          </Button>
        </motion.div>
      )}
    </div>
  )
}
