import React, { useState, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { SEOAnalysisForm } from "./components/SEOAnalysisForm";
import { SEOProgressDisplay } from "./components/SEOProgressDisplay";
import { SEOErrorDisplay } from "./components/SEOErrorDisplay";
import { SEOResultsTabs } from "./components/SEOResultsTabs";
import { useSEOAnalysis } from "./hooks/useSEOAnalysis";
import { usePersistentAnalysis } from "./hooks/usePersistentAnalysis";
import { calculateSEOScore, copyToClipboard as copyToClipboardUtil } from "./utils/seo-helpers";
import { SEOAnalysisResult, AnalysisMode } from "./types/seo";
import SEOAgent from "@/components/tools/seo-agent";
import SEOAnalysisDashboard from "@/components/tools/seo-analysis-dashboard";



export const SEOAnalyzerMain: React.FC = () => {
  const { toast } = useToast();
  
  // State
  const [url, setUrl] = useState("");
  const [analysisMode, setAnalysisMode] = useState<AnalysisMode>("page");
  const [analysisResult, setAnalysisResult] = useState<SEOAnalysisResult | null>(null);

  // Analysis completion handlers
  const handleAnalysisComplete = useCallback((result: SEOAnalysisResult) => {
    setAnalysisResult(result);
    toast({
      title: "Análisis completado",
      description: "El análisis SEO se ha completado exitosamente",
    });
  }, [toast]);

  const handleAnalysisError = useCallback((error: string) => {
    toast({
      title: "Error en el análisis",
      description: error,
      variant: "destructive",
    });
  }, [toast]);

  // Hooks
  const {
    data,
    isLoading,
    isError,
    error,
    analyzeUrl,
    showPulsatingEffect,
    setShowPulsatingEffect,
  } = useSEOAnalysis({ url, analysisMode });

  const {
    persistentAnalysisId,
    persistentProgress,
    persistentLoading,
    persistentError,
    progressLoading,
    progressError,
    startPersistentAnalysis,
    cancelPersistentAnalysis,
    clearPersistentAnalysis,
  } = usePersistentAnalysis({
    url,
    analysisMode,
    onAnalysisComplete: handleAnalysisComplete,
    onAnalysisError: handleAnalysisError,
  });

  // Handle analysis based on mode
  const handleAnalyze = useCallback(() => {
    if (analysisMode === "page") {
      analyzeUrl();
    } else {
      startPersistentAnalysis();
    }
  }, [analysisMode, analyzeUrl, startPersistentAnalysis]);

  // Get current data (either from React Query or persistent analysis)
  const currentData = analysisResult || data;

  // Calculate SEO score
  const getSEOScore = useCallback((): number => {
    return calculateSEOScore(currentData);
  }, [currentData]);

  // Copy to clipboard with toast
  const copyToClipboard = useCallback(async (text: string) => {
    const success = await copyToClipboardUtil(text);
    if (success) {
      toast({
        title: "Copiado",
        description: "Contenido copiado al portapapeles",
      });
    } else {
      toast({
        title: "Error",
        description: "No se pudo copiar al portapapeles",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Check if any loading state is active
  const isAnyLoading = isLoading || progressLoading || persistentLoading;
  const hasError = isError || !!progressError || !!persistentError;
  const hasResults = (currentData && currentData.status === "success");

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Tabs defaultValue="analyzer" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="analyzer">Analizador</TabsTrigger>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
        </TabsList>

        <TabsContent value="analyzer" className="space-y-6">
          {/* Analysis Form */}
          <SEOAnalysisForm
            url={url}
            setUrl={setUrl}
            analysisMode={analysisMode}
            setAnalysisMode={setAnalysisMode}
            onAnalyze={handleAnalyze}
            isLoading={isLoading}
            progressLoading={progressLoading}
            persistentLoading={persistentLoading}
            showPulsatingEffect={showPulsatingEffect}
          />

          {/* Progress Display */}
          <SEOProgressDisplay
            isLoading={isLoading}
            progressLoading={progressLoading}
            persistentLoading={persistentLoading}
            persistentProgress={persistentProgress}
            onCancel={cancelPersistentAnalysis}
          />

          {/* Error Display */}
          <SEOErrorDisplay
            error={error}
            progressError={progressError}
            persistentError={persistentError}
            isError={isError}
          />

          {/* Results */}
          {hasResults && (
            <SEOResultsTabs
              data={currentData}
              url={url}
              getSEOScore={getSEOScore}
              copyToClipboard={copyToClipboard}
            />
          )}

          {/* SEO Agent */}
          {hasResults && (
            <SEOAgent
              analysisComplete={true}
              seoScore={getSEOScore()}
              numIssues={currentData?.recommendations?.length || 0}
            />
          )}

          {!currentData && !isAnyLoading && !hasError && (
            <SEOAgent analysisComplete={false} />
          )}
        </TabsContent>

        <TabsContent value="dashboard" className="space-y-6">
          <SEOAnalysisDashboard />
        </TabsContent>


      </Tabs>
    </div>
  );
};
