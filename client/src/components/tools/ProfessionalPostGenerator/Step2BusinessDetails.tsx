import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import { Upload, ArrowLeft, HelpCircle, <PERSON>rk<PERSON>, Refresh<PERSON><PERSON>, Palette, Image } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import ProgressIndicator from "./components/ProgressIndicator";
import IntelligentEmmaMessage from "./components/IntelligentEmmaMessage";
import { BrandAnalysis } from "./hooks/useBrandData";

interface Step2BusinessDetailsProps {
  businessName: string;
  brandColor: string;
  voice: string;
  analysisComplete: boolean;
  brandAnalysis: BrandAnalysis | null;
  onBusinessNameChange: (value: string) => void;
  onBrandColorChange: (value: string) => void;
  onVoiceChange: (value: string) => void;
  onBack: () => void;
  onNext: () => void;
}

// <PERSON>'s brand colors for quick selection
const EMMA_BRAND_COLORS = [
  { name: "<PERSON> Blue", color: "#3018ef", gradient: "from-[#3018ef] to-[#4c51bf]" },
  { name: "<PERSON> Pink", color: "#dd3a5a", gradient: "from-[#dd3a5a] to-[#e53e3e]" },
  { name: "Purple", color: "#8b5cf6", gradient: "from-[#8b5cf6] to-[#a855f7]" },
  { name: "Teal", color: "#14b8a6", gradient: "from-[#14b8a6] to-[#0d9488]" },
  { name: "Orange", color: "#f97316", gradient: "from-[#f97316] to-[#ea580c]" },
  { name: "Green", color: "#22c55e", gradient: "from-[#22c55e] to-[#16a34a]" },
];

const Step2BusinessDetails: React.FC<Step2BusinessDetailsProps> = ({
  businessName,
  brandColor,
  voice,
  analysisComplete,
  brandAnalysis,
  onBusinessNameChange,
  onBrandColorChange,
  onVoiceChange,
  onBack,
  onNext,
}) => {
  const [logo, setLogo] = useState<string | null>(null);
  const [isGeneratingVoice, setIsGeneratingVoice] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Debug: Log when data changes
  React.useEffect(() => {
    console.log("🔄 Step2 - Data received:", {
      businessName,
      brandColor,
      voice: voice ? voice.substring(0, 50) + '...' : 'Empty',
      analysisComplete,
      hasAnalysis: !!brandAnalysis
    });
  }, [businessName, brandColor, voice, analysisComplete, brandAnalysis]);

  const handleLogoUpload = useCallback((file: File) => {
    if (file && file.type.startsWith('image/')) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        alert('El archivo es muy grande. Por favor selecciona una imagen menor a 5MB.');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setLogo(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      alert('Por favor selecciona un archivo de imagen válido.');
    }
  }, []);

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleLogoUpload(file);
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const file = e.dataTransfer.files[0];
    if (file) {
      handleLogoUpload(file);
    }
  }, [handleLogoUpload]);

  const generateIntelligentVoice = async () => {
    if (!businessName.trim()) {
      alert("Por favor ingresa el nombre del negocio primero");
      return;
    }

    setIsGeneratingVoice(true);

    try {
      const prompt = `
Eres un experto en branding y comunicación. Genera una voz de marca profesional y detallada para "${businessName}".

${brandAnalysis ? `
INFORMACIÓN COMPLETA DEL ANÁLISIS WEB:
- Nombre del negocio: ${brandAnalysis.business_name}
- Industria/Sector: ${brandAnalysis.industry}
- Propuesta de valor: ${brandAnalysis.value_proposition}
- Audiencia objetivo: ${brandAnalysis.target_audience}
- Servicios/Productos: ${brandAnalysis.services_products?.join(', ')}
- Mensajes clave: ${brandAnalysis.key_messages?.join(', ')}
- Nivel de confianza del análisis: ${(brandAnalysis.confidence_score * 100).toFixed(0)}%
` : `
INFORMACIÓN BÁSICA:
- Nombre del negocio: ${businessName}
- Información limitada disponible
`}

INSTRUCCIONES:
Basándote en toda la información anterior, crea una voz de marca que incluya:
1. Personalidad (3-4 adjetivos clave que reflejen la industria y propuesta de valor)
2. Tono de comunicación (formal/informal, técnico/accesible, etc.)
3. Valores que transmite (basados en la propuesta de valor y servicios)
4. Estilo de lenguaje (directo, inspiracional, educativo, etc.)

FORMATO DE RESPUESTA:
Responde ÚNICAMENTE con un párrafo cohesivo de 2-3 oraciones en español que capture la esencia completa de la marca, integrando toda la información del análisis web.
`;

      const response = await fetch('/api/v1/content/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          type: 'brand_voice',
          tone: 'professional',
          audience: brandAnalysis?.target_audience || 'general'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        onVoiceChange(data.result || data.content || data.text || "Voz de marca profesional y confiable.");
      } else {
        // Fallback con lógica inteligente
        const fallbackVoice = generateFallbackVoice();
        onVoiceChange(fallbackVoice);
      }
    } catch (error) {
      console.error('Error generating voice:', error);
      const fallbackVoice = generateFallbackVoice();
      onVoiceChange(fallbackVoice);
    } finally {
      setIsGeneratingVoice(false);
    }
  };

  const generateFallbackVoice = () => {
    const industry = brandAnalysis?.industry || "negocio";
    const businessType = businessName.toLowerCase();

    if (industry.toLowerCase().includes('tecnología') || industry.toLowerCase().includes('software')) {
      return `${businessName} se comunica con un tono innovador y accesible, transmitiendo confianza en la tecnología mientras mantiene un lenguaje claro y directo. Nuestra voz refleja expertise técnico sin perder la calidez humana, posicionándonos como líderes visionarios en ${industry.toLowerCase()}.`;
    } else if (industry.toLowerCase().includes('salud') || industry.toLowerCase().includes('médico')) {
      return `${businessName} adopta una voz profesional y empática, transmitiendo confianza y cuidado en cada comunicación. Nuestro tono es informativo pero cálido, reflejando nuestro compromiso con el bienestar y la excelencia en ${industry.toLowerCase()}.`;
    } else if (industry.toLowerCase().includes('educación')) {
      return `${businessName} se expresa con una voz inspiradora y accesible, fomentando el aprendizaje y el crecimiento. Nuestro tono es motivacional y claro, reflejando nuestra pasión por la educación y el desarrollo personal.`;
    } else {
      return `${businessName} se comunica con una voz profesional y confiable, transmitiendo experiencia y dedicación en ${industry.toLowerCase()}. Nuestro tono es directo pero cercano, reflejando nuestro compromiso con la excelencia y la satisfacción del cliente.`;
    }
  };
  return (
    <div className="max-w-7xl mx-auto px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)'
        }}
      >
        <ProgressIndicator currentStep={2} totalSteps={4} />

        <div className="flex min-h-[600px]">
          {/* Panel izquierdo - Formulario */}
          <div className="w-1/2 p-10">
            <div className="mb-8">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-3">
                Detalles de tu marca
              </h2>
              <p className="text-gray-600 text-lg leading-relaxed">
                Estos detalles se utilizarán para personalizar tus publicaciones y diseños.
              </p>
            </div>

            <IntelligentEmmaMessage
              step={2}
              brandData={{
                businessName,
                brandColor,
                voice,
                topics: [],
                ctas: []
              }}
              brandAnalysis={brandAnalysis}
              analysisComplete={analysisComplete}
              color="blue"
              fallbackMessage="¡Perfecto! Vamos a configurar los detalles de tu marca para crear contenido increíble."
            />

            <div className="space-y-8">
              {/* Enhanced Logo Upload */}
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-4 flex items-center">
                  <Image className="w-4 h-4 mr-2 text-[#3018ef]" />
                  Logo de tu marca
                </label>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileInputChange}
                  accept="image/*"
                  className="hidden"
                />
                <motion.div
                  onClick={() => fileInputRef.current?.click()}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`
                    relative border-2 border-dashed rounded-2xl p-8 text-center cursor-pointer
                    transition-all duration-300 bg-gradient-to-br from-white to-gray-50
                    ${isDragOver
                      ? 'border-[#3018ef] bg-gradient-to-br from-blue-50 to-purple-50 shadow-lg'
                      : logo
                        ? 'border-green-300 bg-gradient-to-br from-green-50 to-emerald-50'
                        : 'border-gray-300 hover:border-[#3018ef] hover:shadow-md'
                    }
                  `}
                >
                  {logo ? (
                    <div className="space-y-4">
                      <div className="relative mx-auto w-20 h-20 rounded-xl overflow-hidden shadow-lg bg-white p-2">
                        <img
                          src={logo}
                          alt="Logo"
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">Logo cargado exitosamente</p>
                        <p className="text-xs text-gray-500 mt-1">Haz clic o arrastra para cambiar</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="mx-auto w-16 h-16 bg-gradient-to-br from-[#3018ef] to-[#dd3a5a] rounded-xl flex items-center justify-center shadow-lg">
                        <Upload className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <p className="text-lg font-semibold text-gray-800">
                          {isDragOver ? 'Suelta tu logo aquí' : 'Sube el logo de tu marca'}
                        </p>
                        <p className="text-sm text-gray-600 mt-2">
                          Arrastra y suelta o haz clic para seleccionar
                        </p>
                        <p className="text-xs text-gray-500 mt-1">PNG, JPG, SVG hasta 5MB</p>
                      </div>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Enhanced Brand Color Picker */}
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-4 flex items-center">
                  <Palette className="w-4 h-4 mr-2 text-[#3018ef]" />
                  Color principal de tu marca
                </label>

                {/* Color Preview */}
                <div className="mb-4">
                  <div
                    className="w-full h-16 rounded-xl shadow-inner border-2 border-white"
                    style={{
                      background: `linear-gradient(135deg, ${brandColor} 0%, ${brandColor}dd 100%)`
                    }}
                  />
                </div>

                {/* Emma Brand Colors */}
                <div className="mb-4">
                  <p className="text-xs font-medium text-gray-600 mb-3">Colores recomendados de Emma</p>
                  <div className="grid grid-cols-3 gap-3">
                    {EMMA_BRAND_COLORS.map((colorOption) => (
                      <motion.button
                        key={colorOption.color}
                        onClick={() => onBrandColorChange(colorOption.color)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className={`
                          relative h-12 rounded-lg bg-gradient-to-r ${colorOption.gradient}
                          shadow-md hover:shadow-lg transition-all duration-200
                          ${brandColor === colorOption.color ? 'ring-2 ring-offset-2 ring-gray-400' : ''}
                        `}
                        title={colorOption.name}
                      >
                        {brandColor === colorOption.color && (
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-4 h-4 bg-white rounded-full shadow-sm" />
                          </div>
                        )}
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Custom Color Input */}
                <div className="flex items-center space-x-3">
                  <input
                    type="color"
                    value={brandColor}
                    onChange={(e) => onBrandColorChange(e.target.value)}
                    className="w-12 h-12 rounded-lg border-2 border-gray-200 cursor-pointer shadow-sm"
                  />
                  <div className="flex-1">
                    <Input
                      value={brandColor}
                      onChange={(e) => onBrandColorChange(e.target.value)}
                      placeholder="#3018ef"
                      className="font-mono text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Business Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3 flex items-center">
                  Business Name
                  <HelpCircle className="w-4 h-4 text-gray-400 ml-1" />
                </label>
                <Input
                  value={businessName}
                  onChange={(e) => onBusinessNameChange(e.target.value)}
                  className="h-12"
                />
              </div>

              {/* Voice */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700 flex items-center">
                    Voice
                    <HelpCircle className="w-4 h-4 text-gray-400 ml-1" />
                  </label>
                  <Button
                    onClick={generateIntelligentVoice}
                    disabled={isGeneratingVoice || !businessName.trim()}
                    variant="outline"
                    size="sm"
                    className="flex items-center text-xs"
                  >
                    {isGeneratingVoice ? (
                      <>
                        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                        Generando...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-3 h-3 mr-1" />
                        Generar con IA
                      </>
                    )}
                  </Button>
                </div>
                <textarea
                  value={voice}
                  onChange={(e) => onVoiceChange(e.target.value)}
                  rows={4}
                  placeholder="Describe el tono y personalidad de tu marca..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3018ef] focus:border-transparent resize-none"
                />
                <p className="text-xs text-gray-500 mt-2">
                  Define cómo se comunica tu marca: tono, personalidad, valores y estilo de lenguaje.
                </p>
              </div>
            </div>
          </div>

          {/* Panel derecho - Preview mejorado */}
          <div className="w-1/2 bg-gradient-to-br from-[#3018ef] via-[#4c51bf] to-[#dd3a5a] p-10 flex items-center justify-center relative overflow-hidden">
            {/* Elementos decorativos de fondo */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
              <div className="absolute bottom-10 right-10 w-24 h-24 bg-white rounded-full blur-2xl"></div>
              <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white rounded-full blur-xl"></div>
            </div>

            <div className="relative z-10 w-full max-w-sm">
              {/* Preview Card */}
              <motion.div
                className="bg-white/15 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                {/* Logo Preview */}
                <div className="flex justify-center mb-6">
                  {logo ? (
                    <div className="w-20 h-20 bg-white rounded-2xl p-3 shadow-lg">
                      <img src={logo} alt="Logo Preview" className="w-full h-full object-contain" />
                    </div>
                  ) : (
                    <div className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center">
                      <Image className="w-8 h-8 text-white/60" />
                    </div>
                  )}
                </div>

                {/* Business Name Preview */}
                <div className="text-center mb-6">
                  <div
                    className="text-xl font-bold text-white mb-2"
                    style={{ textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
                  >
                    {businessName || 'Nombre de tu empresa'}
                  </div>
                  <div className="h-1 w-16 mx-auto rounded-full" style={{ backgroundColor: brandColor }} />
                </div>

                {/* Color Preview */}
                <div className="mb-6">
                  <p className="text-white/80 text-sm mb-3 text-center">Color de marca</p>
                  <div className="flex justify-center">
                    <div
                      className="w-16 h-16 rounded-2xl shadow-lg border-2 border-white/30"
                      style={{ backgroundColor: brandColor }}
                    />
                  </div>
                </div>

                {/* Post Preview Grid */}
                <div className="grid grid-cols-2 gap-3">
                  {[1, 2, 3, 4].map((i) => (
                    <motion.div
                      key={i}
                      className="bg-white/10 rounded-xl h-16 border border-white/20 flex items-center justify-center"
                      whileHover={{ scale: 1.05 }}
                      style={{
                        background: `linear-gradient(135deg, ${brandColor}20 0%, ${brandColor}10 100%)`
                      }}
                    >
                      <div className="w-6 h-6 bg-white/30 rounded" />
                    </motion.div>
                  ))}
                </div>

                <p className="text-white/70 text-xs text-center mt-4">
                  Vista previa de tus publicaciones
                </p>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Footer con botones mejorado */}
        <div className="bg-gradient-to-r from-gray-50 to-white px-10 py-6 border-t border-gray-100 flex justify-between items-center">
          <Button
            onClick={onBack}
            variant="outline"
            className="flex items-center px-6 py-3 border-2 border-gray-300 hover:border-[#3018ef] hover:text-[#3018ef] transition-all duration-200"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Atrás
          </Button>

          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              Paso 2 de 4
            </div>
            <Button
              onClick={onNext}
              disabled={!businessName.trim()}
              className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#2614d4] hover:to-[#c23350] text-white px-8 py-3 font-semibold shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Continuar
              <ArrowLeft className="w-4 h-4 ml-2 rotate-180" />
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Step2BusinessDetails;
