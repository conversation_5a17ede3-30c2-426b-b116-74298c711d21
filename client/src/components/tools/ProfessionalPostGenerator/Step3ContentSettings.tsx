import React from "react";
import { motion } from "framer-motion";
import { ArrowLeft, HelpCircle, MoreHorizontal, Plus, Minus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import ProgressIndicator from "./components/ProgressIndicator";
import IntelligentEmmaMessage from "./components/IntelligentEmmaMessage";
import { BrandAnalysis } from "./hooks/useBrandData";

interface Step3ContentSettingsProps {
  topics: string[];
  ctas: string[];
  analysisComplete: boolean;
  brandAnalysis: BrandAnalysis | null;
  onTopicChange: (index: number, value: string) => void;
  onAddTopic: () => void;
  onCtaChange: (index: number, value: string) => void;
  onAddCta: () => void;
  onRemoveCta: (index: number) => void;
  onBack: () => void;
  onNext: () => void;
  // Datos adicionales para Emma inteligente
  brandData?: {
    businessName: string;
    brandColor: string;
    voice: string;
  };
}

const Step3ContentSettings: React.FC<Step3ContentSettingsProps> = ({
  topics,
  ctas,
  analysisComplete,
  brandAnalysis,
  onTopicChange,
  onAddTopic,
  onCtaChange,
  onAddCta,
  onRemoveCta,
  onBack,
  onNext,
  brandData,
}) => {
  // Debug: Log when data changes
  React.useEffect(() => {
    console.log("🔄 Step3 - Data received:", {
      topics: topics.length > 0 ? topics : 'Empty array',
      ctas: ctas.length > 0 ? ctas : 'Empty array',
      analysisComplete,
      hasAnalysis: !!brandAnalysis
    });
  }, [topics, ctas, analysisComplete, brandAnalysis]);
  return (
    <div className="max-w-6xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden"
      >
        <ProgressIndicator currentStep={3} totalSteps={4} />

        <div className="flex">
          {/* Panel izquierdo */}
          <div className="w-1/2 p-8">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Configuración de Contenido</h2>
              <p className="text-gray-600 mb-4">Estas configuraciones se utilizan al generar contenido para publicaciones específicas y nos guían en los temas sobre los que publicar.</p>
              
              <IntelligentEmmaMessage
                step={3}
                brandData={{
                  businessName: brandData?.businessName || brandAnalysis?.business_name || "Tu marca",
                  brandColor: brandData?.brandColor || "#3018ef",
                  voice: brandData?.voice || "",
                  topics,
                  ctas
                }}
                brandAnalysis={brandAnalysis}
                analysisComplete={analysisComplete}
                color="purple"
                fallbackMessage="¡Perfecto! Vamos a configurar los temas y CTAs para tu contenido."
              />
            </div>

            {/* Topics */}
            <div className="mb-6">
              <h3 className="text-gray-900 font-semibold mb-4">Temas de Contenido</h3>
              <div className="space-y-3">
                {topics.map((topic, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Input
                      value={topic}
                      onChange={(e) => onTopicChange(index, e.target.value)}
                      className="flex-1"
                      placeholder="Ingresa un tema..."
                    />
                    <button className="text-gray-400 hover:text-gray-600">
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
              <button
                onClick={onAddTopic}
                className="text-purple-600 hover:text-purple-700 text-sm mt-3 flex items-center"
              >
                Ver los 7 temas restantes
              </button>
            </div>

            {/* Call to Actions (CTAs) */}
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <h3 className="text-gray-900 font-semibold">Llamadas a la Acción (CTAs)</h3>
                <HelpCircle className="w-4 h-4 text-gray-400 ml-2" />
              </div>
              <div className="space-y-3">
                {ctas.map((cta, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Input
                      value={cta}
                      onChange={(e) => onCtaChange(index, e.target.value)}
                      className="flex-1"
                      placeholder="Ingresa una llamada a la acción..."
                    />
                    <button
                      onClick={() => onRemoveCta(index)}
                      className="text-red-400 hover:text-red-600"
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
              <button
                onClick={onAddCta}
                className="text-gray-600 hover:text-gray-800 text-sm mt-3 flex items-center"
              >
                <Plus className="w-4 h-4 mr-1" />
                Nueva CTA
              </button>
            </div>
          </div>

          {/* Panel derecho - Preview */}
          <div className="w-1/2 bg-gradient-to-br from-purple-400 to-purple-600 p-8 flex items-center justify-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 w-full max-w-sm">
              <div className="bg-white/20 rounded-lg h-64 mb-4 flex items-end p-4">
                <div className="w-full space-y-2">
                  <div className="bg-white/30 rounded h-3 w-full"></div>
                  <div className="bg-white/30 rounded h-3 w-3/4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer con botones */}
        <div className="bg-gray-50 px-8 py-4 border-t border-gray-200 flex justify-between">
          <Button
            onClick={onBack}
            variant="outline"
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Atrás
          </Button>
          <Button
            onClick={onNext}
            className="bg-black hover:bg-gray-800 text-white px-8"
          >
            Continuar
          </Button>
        </div>
      </motion.div>
    </div>
  );
};

export default Step3ContentSettings;
