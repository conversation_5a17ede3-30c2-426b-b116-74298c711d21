import React from "react";
import PostGeneratorResultsContainer from "./PostGeneratorResults/";

interface PostGeneratorResultsProps {
  brandData: any;
  selectedTheme: string;
  selectedContentType: string;
  onBack: () => void;
}

const PostGeneratorResults: React.FC<PostGeneratorResultsProps> = ({
  brandData,
  selectedTheme,
  selectedContentType,
  onBack,
}) => {
  return (
    <PostGeneratorResultsContainer
      brandData={brandData}
      selectedTheme={selectedTheme}
      selectedContentType={selectedContentType}
      onBack={onBack}
    />
  );
};

export default PostGeneratorResults;
