"use client";

import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON>bs<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import {
  Clock,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Trash2,
  ExternalLink,
  RefreshCw,
  Activity,
  Globe,
  FileText,
  Calendar,
  Timer
} from "lucide-react";

interface SEOAnalysis {
  id: number;
  analysis_id: string;
  user_id?: string;
  url: string;
  mode: string;
  status: string;
  current_page: number;
  total_pages: number;
  phase?: string;
  status_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  estimated_completion?: string;
  processing_time?: number;
  pages_analyzed: number;
  total_pages_found: number;
  failed_urls_count: number;
  ai_enhanced: boolean;
}

export default function SEOAnalysisDashboard() {
  const [activeTab, setActiveTab] = useState("active");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch user analyses
  const { data: analysesData, isLoading, refetch } = useQuery({
    queryKey: ["seo-analyses"],
    queryFn: async () => {
      const response = await fetch("/api/seo/analyses");
      if (!response.ok) {
        throw new Error("Failed to fetch analyses");
      }
      return response.json();
    },
    refetchInterval: 5000, // Refetch every 5 seconds for real-time updates
  });

  // Cancel analysis mutation
  const cancelMutation = useMutation({
    mutationFn: async (analysisId: string) => {
      const response = await fetch(`/api/seo/analyses/${analysisId}`, {
        method: "DELETE",
      });
      if (!response.ok) {
        throw new Error("Failed to cancel analysis");
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Análisis cancelado",
        description: "El análisis ha sido cancelado exitosamente",
      });
      queryClient.invalidateQueries({ queryKey: ["seo-analyses"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const analyses = analysesData?.analyses || [];

  // Filter analyses by status
  const activeAnalyses = analyses.filter((a: SEOAnalysis) => 
    ["pending", "in_progress"].includes(a.status)
  );
  const completedAnalyses = analyses.filter((a: SEOAnalysis) => 
    a.status === "complete"
  );
  const failedAnalyses = analyses.filter((a: SEOAnalysis) => 
    ["error", "cancelled"].includes(a.status)
  );

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "in_progress":
        return <Activity className="h-4 w-4 text-blue-500 animate-spin" />;
      case "complete":
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "cancelled":
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      pending: "outline",
      in_progress: "default",
      complete: "secondary",
      error: "destructive",
      cancelled: "outline",
    };

    const labels: Record<string, string> = {
      pending: "Pendiente",
      in_progress: "En progreso",
      complete: "Completado",
      error: "Error",
      cancelled: "Cancelado",
    };

    return (
      <Badge variant={variants[status] || "outline"}>
        {labels[status] || status}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("es-ES", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)}m`;
    } else {
      return `${Math.round(seconds / 3600)}h`;
    }
  };

  const getProgress = (analysis: SEOAnalysis) => {
    if (analysis.status === "complete") return 100;
    if (analysis.status === "error" || analysis.status === "cancelled") return 0;
    if (analysis.total_pages === 0) return 0;
    return Math.round((analysis.current_page / analysis.total_pages) * 100);
  };

  const AnalysisCard = ({ analysis }: { analysis: SEOAnalysis }) => (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {getStatusIcon(analysis.status)}
              <CardTitle className="text-lg truncate">{analysis.url}</CardTitle>
              {getStatusBadge(analysis.status)}
            </div>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                {analysis.mode === "website" ? (
                  <Globe className="h-3 w-3" />
                ) : (
                  <FileText className="h-3 w-3" />
                )}
                {analysis.mode === "website" ? "Sitio completo" : "Página específica"}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {formatDate(analysis.created_at)}
              </div>
              {analysis.processing_time && (
                <div className="flex items-center gap-1">
                  <Timer className="h-3 w-3" />
                  {formatDuration(analysis.processing_time)}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {analysis.status === "complete" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(`/dashboard/herramientas/seo-analyzer?analysis=${analysis.analysis_id}`, "_blank")}
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Ver resultados
              </Button>
            )}
            {["pending", "in_progress"].includes(analysis.status) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => cancelMutation.mutate(analysis.analysis_id)}
                disabled={cancelMutation.isPending}
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Cancelar
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {["pending", "in_progress"].includes(analysis.status) && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{analysis.status_message || "Procesando..."}</span>
              <span>{analysis.current_page}/{analysis.total_pages}</span>
            </div>
            <Progress value={getProgress(analysis)} className="h-2" />
            {analysis.phase && (
              <div className="text-xs text-muted-foreground">
                Fase: {analysis.phase}
              </div>
            )}
          </div>
        )}
        
        {analysis.status === "complete" && (
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <div className="font-medium">Páginas analizadas</div>
              <div className="text-muted-foreground">{analysis.pages_analyzed}</div>
            </div>
            <div>
              <div className="font-medium">Total encontradas</div>
              <div className="text-muted-foreground">{analysis.total_pages_found}</div>
            </div>
            <div>
              <div className="font-medium">IA mejorada</div>
              <div className="text-muted-foreground">
                {analysis.ai_enhanced ? "Sí" : "No"}
              </div>
            </div>
          </div>
        )}
        
        {analysis.status === "error" && (
          <div className="text-sm text-red-600">
            {analysis.status_message || "Error durante el análisis"}
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        Cargando análisis...
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Mis Análisis SEO</h2>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Actualizar
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="active">
            Activos ({activeAnalyses.length})
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completados ({completedAnalyses.length})
          </TabsTrigger>
          <TabsTrigger value="failed">
            Fallidos ({failedAnalyses.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          <ScrollArea className="h-[600px]">
            {activeAnalyses.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No hay análisis activos
              </div>
            ) : (
              activeAnalyses.map((analysis: SEOAnalysis) => (
                <AnalysisCard key={analysis.analysis_id} analysis={analysis} />
              ))
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <ScrollArea className="h-[600px]">
            {completedAnalyses.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No hay análisis completados
              </div>
            ) : (
              completedAnalyses.map((analysis: SEOAnalysis) => (
                <AnalysisCard key={analysis.analysis_id} analysis={analysis} />
              ))
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="failed" className="space-y-4">
          <ScrollArea className="h-[600px]">
            {failedAnalyses.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No hay análisis fallidos
              </div>
            ) : (
              failedAnalyses.map((analysis: SEOAnalysis) => (
                <AnalysisCard key={analysis.analysis_id} analysis={analysis} />
              ))
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
}
