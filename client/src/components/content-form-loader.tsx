import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, Sparkles } from "lucide-react";
import { TemplateQuestion } from "@/data/category-questions";
import contentFormData, { getGenericForm } from "@/data/content-form-questions";
import contentFormQuestionsExtra from "@/data/content-form-questions-extra";
import contentFormQuestionsExtra2 from "@/data/content-form-questions-extra2";
import contentFormQuestionsExtra3 from "@/data/content-form-questions-extra3";
import contentFormQuestionsExtra4 from "@/data/content-form-questions-extra4";
import { marketingFunctionQuestions } from "@/data/marketing-function-questions";
import { marketingQuestionsNewBatch } from "@/data/marketing-questions-new-batch";
import {
  findSubcategoryById,
  findCategoryBySubcategoryId,
} from "@/data/category-questions";
import ContentFormSimple from "./content-form-simple";

interface ContentFormLoaderProps {
  contentTypeId: string;
  onCancel?: () => void;
  onSubmit?: (data: any) => void;
}

export default function ContentFormLoader({
  contentTypeId,
  onCancel,
  onSubmit,
}: ContentFormLoaderProps) {
  const [formData, setFormData] = useState<TemplateQuestion[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setIsLoading(true);
    setError(null);
    setFormData(null);

    // Pequeño timeout para permitir que la UI se actualice
    setTimeout(() => {
      try {
        // Buscar el formulario en cada módulo
        let formToUse = null;

        console.log("Buscando formulario para ID:", contentTypeId);

        // 1. Verificar en el objeto base primero
        if (contentFormData[contentTypeId]) {
          console.log("Formulario encontrado en contentFormData");
          formToUse = contentFormData[contentTypeId];
        }
        // 2. Verificar en extra1
        else if (contentFormQuestionsExtra[contentTypeId]) {
          console.log("Formulario encontrado en contentFormQuestionsExtra");
          formToUse = contentFormQuestionsExtra[contentTypeId];
        }
        // 3. Verificar en extra2
        else if (contentFormQuestionsExtra2[contentTypeId]) {
          console.log("Formulario encontrado en contentFormQuestionsExtra2");
          formToUse = contentFormQuestionsExtra2[contentTypeId];
        }
        // 4. Verificar en extra3
        else if (contentFormQuestionsExtra3[contentTypeId]) {
          console.log("Formulario encontrado en contentFormQuestionsExtra3");
          formToUse = contentFormQuestionsExtra3[contentTypeId];
        }
        // 5. Verificar en extra4
        else if (contentFormQuestionsExtra4[contentTypeId]) {
          console.log("Formulario encontrado en contentFormQuestionsExtra4");
          formToUse = contentFormQuestionsExtra4[contentTypeId];
        }
        // 6. Verificar en marketing function questions
        else if (marketingFunctionQuestions[contentTypeId]) {
          console.log("Formulario encontrado en marketingFunctionQuestions");
          formToUse = marketingFunctionQuestions[contentTypeId];
        }
        // 7. Verificar en marketing questions new batch
        else if (marketingQuestionsNewBatch[contentTypeId]) {
          console.log("Formulario encontrado en marketingQuestionsNewBatch");
          formToUse = marketingQuestionsNewBatch[contentTypeId];
        }

        // 8. Si no se encuentra, usar genérico
        if (!formToUse) {
          console.log("No se encontró formulario, generando uno genérico para:", contentTypeId);
          formToUse = getGenericForm(contentTypeId);
        }

        // Completar la carga
        if (formToUse) {
          setFormData(formToUse);
        } else {
          setError("No se pudo encontrar un formulario para el tipo de contenido seleccionado.");
        }
      } catch (error) {
        console.error("Error al cargar formulario:", error);
        setError("Ocurrió un error al cargar el formulario.");
      } finally {
        setIsLoading(false);
      }
    }, 100);
  }, [contentTypeId]);

  // Mostrar pantalla de carga
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-12 text-center">
        <Sparkles className="h-12 w-12 text-amber-500 mb-4 animate-pulse" />
        <h2 className="text-2xl font-bold mb-2">Preparando formulario</h2>
        <p className="text-muted-foreground mb-6">
          Estamos preparando el formulario para este tipo de contenido. Por favor espera un momento...
        </p>
        {onCancel && (
          <Button onClick={onCancel}>
            <ChevronLeft className="mr-2 h-4 w-4" /> Volver
          </Button>
        )}
      </div>
    );
  }

  // Mostrar mensaje de error
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-12 text-center">
        <div className="h-12 w-12 text-red-500 mb-4 flex items-center justify-center rounded-full border-2 border-red-500">
          <span className="text-lg font-bold">!</span>
        </div>
        <h2 className="text-2xl font-bold mb-2">Error al cargar formulario</h2>
        <p className="text-muted-foreground mb-6">{error}</p>
        {onCancel && (
          <Button onClick={onCancel}>
            <ChevronLeft className="mr-2 h-4 w-4" /> Volver
          </Button>
        )}
      </div>
    );
  }

  // Renderizar el formulario cuando los datos están disponibles
  return (
    <ContentFormSimple
      contentTypeId={contentTypeId}
      formDataProp={formData}
      onCancel={onCancel}
      onSubmit={onSubmit}
    />
  );
}
