import React from "react";
import { observer } from "mobx-react-lite";
import { SectionTab } from "polotno/side-panel";
import { Button } from "polotno/toolbar/button";

// Emma branded templates and assets
const emmaTemplates = [
  {
    id: "emma-social-1",
    name: "Emma Social Post",
    preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMzAxOGVmIi8+Cjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RW1tYTwvdGV4dD4KPC9zdmc+",
    width: 1080,
    height: 1080,
    elements: [
      {
        type: "rect",
        x: 0,
        y: 0,
        width: 1080,
        height: 1080,
        fill: "linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)",
      },
      {
        type: "text",
        x: 540,
        y: 540,
        width: 800,
        height: 100,
        text: "Emma Studio",
        fontSize: 72,
        fontFamily: "Arial",
        fontWeight: "bold",
        fill: "#ffffff",
        align: "center",
      },
    ],
  },
  {
    id: "emma-story-1",
    name: "<PERSON>",
    preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgNjAgMTAwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iNjAiIGhlaWdodD0iMTAwIiBmaWxsPSIjZGQzYTVhIi8+Cjx0ZXh0IHg9IjMwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+U3Rvcnk8L3RleHQ+Cjwvc3ZnPg==",
    width: 1080,
    height: 1920,
    elements: [
      {
        type: "rect",
        x: 0,
        y: 0,
        width: 1080,
        height: 1920,
        fill: "linear-gradient(180deg, #dd3a5a 0%, #3018ef 100%)",
      },
      {
        type: "text",
        x: 540,
        y: 960,
        width: 900,
        height: 120,
        text: "Emma Story",
        fontSize: 84,
        fontFamily: "Arial",
        fontWeight: "bold",
        fill: "#ffffff",
        align: "center",
      },
    ],
  },
  {
    id: "emma-banner-1",
    name: "Emma Banner",
    preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjMwIiB2aWV3Qm94PSIwIDAgMTAwIDMwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjMzAxOGVmIi8+Cjx0ZXh0IHg9IjUwIiB5PSIyMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+QmFubmVyPC90ZXh0Pgo8L3N2Zz4=",
    width: 1200,
    height: 400,
    elements: [
      {
        type: "rect",
        x: 0,
        y: 0,
        width: 1200,
        height: 400,
        fill: "linear-gradient(90deg, #3018ef 0%, #dd3a5a 100%)",
      },
      {
        type: "text",
        x: 600,
        y: 200,
        width: 1000,
        height: 80,
        text: "Emma Marketing Banner",
        fontSize: 48,
        fontFamily: "Arial",
        fontWeight: "bold",
        fill: "#ffffff",
        align: "center",
      },
    ],
  },
];

// Emma brand colors
const emmaBrandColors = [
  "#3018ef", // Emma Blue
  "#dd3a5a", // Emma Pink/Red
  "#ffffff", // White
  "#000000", // Black
  "#f8f9fa", // Light Gray
  "#6c757d", // Medium Gray
  "#28a745", // Success Green
  "#ffc107", // Warning Yellow
  "#17a2b8", // Info Cyan
  "#dc3545", // Danger Red
];



// Create a proper Polotno section
export const EmmaSection = {
  name: "emma",
  Tab: (props: any) => (
    <SectionTab name="emma" {...props}>
      🎨
    </SectionTab>
  ),
  Panel: observer(({ store }: { store: any }) => {
    const addTemplate = (template: any) => {
      // Clear current page
      store.pages[0].children.clear();

      // Set page size
      store.pages[0].set({
        width: template.width,
        height: template.height,
      });

      // Add template elements
      template.elements.forEach((element: any) => {
        store.pages[0].addElement(element);
      });
    };

    const addBrandColor = (color: string) => {
      const selectedElements = store.selectedElements;
      if (selectedElements.length > 0) {
        selectedElements.forEach((element: any) => {
          if (element.type === "text") {
            element.set({ fill: color });
          } else if (element.type === "rect" || element.type === "circle") {
            element.set({ fill: color });
          }
        });
      }
    };

    return (
      <div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
        {/* Emma Branding Header */}
        <div style={{
          padding: "15px",
          background: "linear-gradient(135deg, #3018ef 0%, #dd3a5a 100%)",
          color: "white",
          textAlign: "center",
        }}>
          <h3 style={{ margin: 0, fontSize: "16px", fontWeight: "bold" }}>
            Emma Studio
          </h3>
          <p style={{ margin: "5px 0 0 0", fontSize: "12px", opacity: 0.9 }}>
            Professional Templates & Brand Assets
          </p>
        </div>

        {/* Templates Section */}
        <div style={{ padding: "15px", borderBottom: "1px solid #e1e5e9" }}>
          <h4 style={{ margin: "0 0 10px 0", fontSize: "14px", fontWeight: "600" }}>
            Emma Templates
          </h4>
          <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, 1fr)",
            gap: "10px",
          }}>
            {emmaTemplates.map((template) => (
              <div
                key={template.id}
                onClick={() => addTemplate(template)}
                style={{
                  cursor: "pointer",
                  border: "1px solid #e1e5e9",
                  borderRadius: "6px",
                  overflow: "hidden",
                  transition: "all 0.2s",
                  backgroundColor: "#fff",
                }}
              >
                <img
                  src={template.preview}
                  alt={template.name}
                  style={{
                    width: "100%",
                    height: "60px",
                    objectFit: "cover",
                  }}
                />
                <div style={{ padding: "8px" }}>
                  <p style={{
                    margin: 0,
                    fontSize: "11px",
                    fontWeight: "500",
                    color: "#333",
                    textAlign: "center",
                  }}>
                    {template.name}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Brand Colors Section */}
        <div style={{ padding: "15px" }}>
          <h4 style={{ margin: "0 0 10px 0", fontSize: "14px", fontWeight: "600" }}>
            Emma Brand Colors
          </h4>
          <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(5, 1fr)",
            gap: "8px",
          }}>
            {emmaBrandColors.map((color, index) => (
              <div
                key={index}
                onClick={() => addBrandColor(color)}
                style={{
                  width: "30px",
                  height: "30px",
                  backgroundColor: color,
                  borderRadius: "6px",
                  cursor: "pointer",
                  border: color === "#ffffff" ? "1px solid #e1e5e9" : "none",
                  transition: "all 0.2s",
                }}
                title={color}
              />
            ))}
          </div>
          <p style={{
            margin: "10px 0 0 0",
            fontSize: "11px",
            color: "#666",
            textAlign: "center",
          }}>
            Click colors to apply to selected elements
          </p>
        </div>
      </div>
    );
  }),
};

export default EmmaSection;
