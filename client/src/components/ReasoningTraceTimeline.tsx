import React from 'react';
import { Clock, CheckCircle } from 'lucide-react';

interface ReasoningStep {
  id: string;
  type: string;
  title: string;
  description: string;
  time: string;
}

interface ReasoningTraceTimelineProps {
  steps: ReasoningStep[];
}

const ReasoningTraceTimeline: React.FC<ReasoningTraceTimelineProps> = ({ steps }) => {
  return (
    <div className="space-y-4">
      {steps.map((step, index) => (
        <div key={step.id} className="flex gap-3">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-purple-600" />
            </div>
            {index < steps.length - 1 && (
              <div className="w-px h-6 bg-gray-200 mt-2" />
            )}
          </div>
          <div className="flex-1 pb-4">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-sm text-gray-900">{step.title}</h4>
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Clock className="h-3 w-3" />
                {step.time}
              </div>
            </div>
            <p className="text-sm text-gray-600 leading-relaxed">
              {step.description}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ReasoningTraceTimeline;
