/**
 * Componente simplificado para transferencia de estilo
 * Compatible con Chrome y Safari
 */

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/hooks/use-toast';
import {
  Upload,
  Wand2,
  Loader2,
  Download,
  Copy,
  Palette,
  Sparkles,
  ArrowRight,
} from 'lucide-react';

export default function StyleTransferSimple() {
  const [initImage, setInitImage] = useState<File | null>(null);
  const [initImagePreview, setInitImagePreview] = useState<string | null>(null);
  const [styleImage, setStyleImage] = useState<File | null>(null);
  const [styleImagePreview, setStyleImagePreview] = useState<string | null>(null);
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [styleStrength, setStyleStrength] = useState([1.0]);
  const [compositionFidelity, setCompositionFidelity] = useState([0.9]);
  const [changeStrength, setChangeStrength] = useState([0.9]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);

  const initImageInputRef = useRef<HTMLInputElement>(null);
  const styleImageInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Manejar selección de imagen original
  const handleInitImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setInitImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setInitImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Manejar selección de imagen de estilo
  const handleStyleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setStyleImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setStyleImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Función simplificada de generación
  const handleGenerate = async () => {
    if (!initImage || !styleImage) {
      toast({
        title: "❌ Error",
        description: "Debes subir tanto la imagen original como la imagen de estilo.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setGeneratedImage(null);

    try {
      // Crear FormData
      const formData = new FormData();
      formData.append('init_image', initImage);
      formData.append('style_image', styleImage);
      formData.append('prompt', prompt.trim());
      
      if (negativePrompt.trim()) {
        formData.append('negative_prompt', negativePrompt.trim());
      }
      
      formData.append('style_strength', styleStrength[0].toString());
      formData.append('composition_fidelity', compositionFidelity[0].toString());
      formData.append('change_strength', changeStrength[0].toString());
      formData.append('seed', '0');
      formData.append('output_format', 'png');

      console.log('Enviando solicitud a Stability AI...');

      const response = await fetch('/api/v1/images/style-transfer', {
        method: 'POST',
        body: formData,
      });

      console.log('Respuesta recibida:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error del servidor:', errorText);
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Datos recibidos:', data);

      if (!data.success) {
        throw new Error(data.error || 'Error en la transferencia de estilo');
      }

      // Crear URL de la imagen desde base64
      const imageUrl = `data:image/png;base64,${data.image}`;
      setGeneratedImage(imageUrl);

      toast({
        title: "✅ ¡Éxito!",
        description: "Estilo transferido exitosamente.",
      });

    } catch (error) {
      console.error('Error en transferencia de estilo:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error desconocido al transferir estilo.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Copiar imagen al portapapeles
  const copyToClipboard = async () => {
    if (!generatedImage) return;

    try {
      await navigator.clipboard.writeText(generatedImage);
      toast({
        title: "📋 ¡Copiado!",
        description: "URL de imagen copiada al portapapeles.",
      });
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "No se pudo copiar la imagen.",
        variant: "destructive",
      });
    }
  };

  // Descargar imagen
  const downloadImage = () => {
    if (!generatedImage) return;

    try {
      const link = document.createElement('a');
      link.href = generatedImage;
      link.download = `style-transfer-${Date.now()}.png`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      setTimeout(() => document.body.removeChild(link), 100);

      toast({
        title: "💾 ¡Descargado!",
        description: "Imagen descargada exitosamente.",
      });
    } catch (error) {
      toast({
        title: "❌ Error",
        description: "No se pudo descargar la imagen.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Panel de imágenes */}
        <div className="lg:col-span-2 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Imagen original */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="w-5 h-5" />
                  Imagen Original
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                  onClick={() => initImageInputRef.current?.click()}
                >
                  {initImagePreview ? (
                    <div className="space-y-3">
                      <img
                        src={initImagePreview}
                        alt="Imagen original"
                        className="max-w-full max-h-48 mx-auto rounded-lg object-contain"
                      />
                      <p className="text-sm text-gray-600">
                        {initImage?.name} ({Math.round((initImage?.size || 0) / 1024)} KB)
                      </p>
                      <Button variant="outline" size="sm">
                        Cambiar imagen
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                      <div>
                        <p className="text-lg font-medium text-gray-700">
                          Sube tu imagen original
                        </p>
                        <p className="text-sm text-gray-500">
                          JPEG, PNG o WebP (máx. 10MB)
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                
                <input
                  ref={initImageInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleInitImageSelect}
                  className="hidden"
                />
              </CardContent>
            </Card>

            {/* Imagen de estilo */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Imagen de Estilo
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                  onClick={() => styleImageInputRef.current?.click()}
                >
                  {styleImagePreview ? (
                    <div className="space-y-3">
                      <img
                        src={styleImagePreview}
                        alt="Imagen de estilo"
                        className="max-w-full max-h-48 mx-auto rounded-lg object-contain"
                      />
                      <p className="text-sm text-gray-600">
                        {styleImage?.name} ({Math.round((styleImage?.size || 0) / 1024)} KB)
                      </p>
                      <Button variant="outline" size="sm">
                        Cambiar imagen
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                      <div>
                        <p className="text-lg font-medium text-gray-700">
                          Sube tu imagen de estilo
                        </p>
                        <p className="text-sm text-gray-500">
                          JPEG, PNG o WebP (máx. 10MB)
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                
                <input
                  ref={styleImageInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleStyleImageSelect}
                  className="hidden"
                />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Panel de configuración y resultado */}
        <div className="space-y-6">
          {/* Configuración */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="w-5 h-5" />
                Configuración
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="prompt">Prompt (opcional)</Label>
                <Textarea
                  id="prompt"
                  placeholder="Ej: Un retrato artístico con colores vibrantes"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-[80px] mt-2"
                />
              </div>

              <div>
                <Label htmlFor="negative-prompt">Prompt negativo (opcional)</Label>
                <Textarea
                  id="negative-prompt"
                  placeholder="Ej: borroso, de baja calidad, distorsionado"
                  value={negativePrompt}
                  onChange={(e) => setNegativePrompt(e.target.value)}
                  className="min-h-[60px] mt-2"
                />
              </div>

              {/* Controles avanzados */}
              <div className="space-y-4 pt-4 border-t">
                <div>
                  <Label>Fuerza del estilo: {styleStrength[0].toFixed(2)}</Label>
                  <Slider
                    value={styleStrength}
                    onValueChange={setStyleStrength}
                    min={0}
                    max={1}
                    step={0.05}
                    className="w-full mt-2"
                  />
                </div>

                <div>
                  <Label>Fidelidad de composición: {compositionFidelity[0].toFixed(2)}</Label>
                  <Slider
                    value={compositionFidelity}
                    onValueChange={setCompositionFidelity}
                    min={0}
                    max={1}
                    step={0.05}
                    className="w-full mt-2"
                  />
                </div>

                <div>
                  <Label>Fuerza de cambio: {changeStrength[0].toFixed(2)}</Label>
                  <Slider
                    value={changeStrength}
                    onValueChange={setChangeStrength}
                    min={0.1}
                    max={1}
                    step={0.05}
                    className="w-full mt-2"
                  />
                </div>
              </div>

              {/* Botón de generar */}
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !initImage || !styleImage}
                className="w-full h-12 text-lg"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Transfiriendo estilo...
                  </>
                ) : (
                  <>
                    <ArrowRight className="w-5 h-5 mr-2" />
                    Transferir Estilo
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Resultado */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Resultado
              </CardTitle>
            </CardHeader>
            <CardContent>
              {generatedImage ? (
                <div className="space-y-4">
                  <div className="relative group">
                    <img
                      src={generatedImage}
                      alt="Imagen generada"
                      className="w-full rounded-lg shadow-lg"
                    />
                  </div>

                  <div className="flex gap-2 justify-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyToClipboard}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copiar
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={downloadImage}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Descargar
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="aspect-square bg-gray-50 rounded-lg flex items-center justify-center">
                  <div className="text-center space-y-3">
                    <ArrowRight className="w-16 h-16 text-gray-300 mx-auto" />
                    <p className="text-gray-500">
                      Tu imagen con estilo transferido aparecerá aquí
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
