/**
 * Componente de prueba simplificado para transferencia de estilo
 * Para debugging de compatibilidad con Chrome
 */

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload } from 'lucide-react';

export default function StyleTransferTest() {
  const [message, setMessage] = useState('Componente cargado correctamente');

  const testFunction = () => {
    console.log('Test function called');
    setMessage('Función de prueba ejecutada - ' + new Date().toLocaleTimeString());
  };

  const testLocalStorage = () => {
    try {
      localStorage.setItem('test', 'value');
      const value = localStorage.getItem('test');
      localStorage.removeItem('test');
      setMessage(`LocalStorage funciona: ${value}`);
    } catch (error) {
      setMessage(`Error en localStorage: ${error}`);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Transferir Estilo - Prueba
        </h1>
        <p className="text-gray-600">
          Componente de prueba para debugging
        </p>
      </div>

      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Prueba de Funcionalidad
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700">{message}</p>
          </div>

          <div className="space-y-2">
            <Button onClick={testFunction} className="w-full">
              Probar Función
            </Button>
            
            <Button onClick={testLocalStorage} variant="outline" className="w-full">
              Probar LocalStorage
            </Button>
          </div>

          <div className="text-xs text-gray-500">
            <p>Navegador: {navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Otro'}</p>
            <p>LocalStorage disponible: {typeof localStorage !== 'undefined' ? 'Sí' : 'No'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
