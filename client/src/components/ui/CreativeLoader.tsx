import React from "react";

const CreativeLoader: React.FC = () => {
  return (
    <div className="relative w-40 h-40 mx-auto">
      <svg 
        className="w-full h-full" 
        viewBox="0 0 160 160" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Rotating circles */}
        <g className="animate-spin" style={{ transformOrigin: '80px 80px', animationDuration: '3s' }}>
          <circle 
            cx="80" 
            cy="80" 
            r="45" 
            fill="none" 
            stroke="#3018ef" 
            strokeWidth="3" 
            strokeDasharray="282.74" 
            strokeDashoffset="282.74"
            className="animate-pulse"
            style={{ animationDuration: '2s' }}
          />
          <circle 
            cx="80" 
            cy="80" 
            r="52" 
            fill="none" 
            stroke="#dd3a5a" 
            strokeWidth="3" 
            strokeDasharray="326.73" 
            strokeDashoffset="326.73"
            className="animate-pulse"
            style={{ animationDuration: '2.5s' }}
          />
          <circle 
            cx="80" 
            cy="80" 
            r="38" 
            fill="none" 
            stroke="#4ECDC4" 
            strokeWidth="3" 
            strokeDasharray="238.76" 
            strokeDashoffset="238.76"
            className="animate-pulse"
            style={{ animationDuration: '1.8s' }}
          />
        </g>
        
        {/* Center dot */}
        <circle 
          cx="80" 
          cy="80" 
          r="4" 
          fill="#dd3a5a"
          className="animate-bounce"
        />
        
        {/* Drawing line */}
        <path 
          d="M140 140L20 20" 
          stroke="#3018ef" 
          strokeWidth="3" 
          strokeLinecap="round" 
          strokeDasharray="169.74" 
          strokeDashoffset="169.74"
          className="animate-pulse"
          style={{ animationDuration: '3s' }}
        />
      </svg>
      
      {/* Additional animated elements */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-2 h-2 bg-purple-500 rounded-full animate-ping"></div>
      </div>
    </div>
  );
};

export default CreativeLoader;
