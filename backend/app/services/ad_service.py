"""
Service for creating advertisements using OpenAI's gpt-image-1 model.
Supports initial generation, multi-turn editing, streaming, and mask-based editing.
Specialized in professional product placement and agency-style advertising.
"""

import logging
import base64
import httpx
import io
import tempfile
import os
from typing import Optional, List, Dict, Any, AsyncGenerator
from app.core.config import settings
from fastapi import UploadFile
import json

logger = logging.getLogger(__name__)


class AdService:
    """Service for creating and editing advertisements using OpenAI's gpt-image-1 model."""
    
    def __init__(self):
        self.api_key = settings.OPENAI_API_KEY
        self.base_url = "https://api.openai.com/v1"
        
    async def generate_ad(self, prompt: str, size: str = "auto") -> Dict[str, Any]:
        """
        Generate an initial advertisement using gpt-image-1.
        
        Args:
            prompt: Description of the advertisement to create
            size: Image size (1024x1024, 1792x1024, 1024x1792)
            
        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("OpenAI API key not configured")
            return {"success": False, "error": "OpenAI API key not configured"}
        
        try:
            # Mejorar el prompt para generación profesional de anuncios
            enhanced_prompt = f"""Crea una imagen de anuncio profesional y de alta calidad con el siguiente concepto: {prompt}.

Requisitos de estilo:
- Calidad de producción a nivel de agencia profesional
- Enfoque en product placement con el producto como protagonista
- Composición limpia, moderna y sofisticada
- Iluminación profesional y sombras
- Estética de fotografía comercial de alta gama
- Fondo contextual que realce el producto

Estilos visuales a considerar según el tipo de producto:
- Electrónicos/Tecnología: Fondos minimalistas, superficies limpias, iluminación moderna
- Moda/Estilo de vida: Ambientes elegantes, iluminación natural, contextos de estilo de vida
- Comida/Bebidas: Presentación apetitosa, iluminación cálida, accesorios contextuales
- Artículos de lujo: Materiales premium, fondos sofisticados, iluminación dramática
- Gaming/Entretenimiento: Fondos dinámicos, acentos de neón, composición energética

Especificaciones técnicas:
- Apariencia de ultra alta resolución
- Gradación de color profesional
- Enfoque nítido en el producto principal
- Composición equilibrada siguiendo la regla de los tercios
- Estándares de fotografía comercial
- Calidad lista para impresión y digital

La imagen final debe verse como si fuera creada por una agencia de publicidad de primer nivel para una campaña de marca importante."""

            payload = {
                "model": "gpt-image-1",
                "prompt": enhanced_prompt,
                "n": 1,
                "size": size,
                "quality": "auto",  # auto, low, medium, high
                "output_format": "png",  # png, jpeg, webp
                "background": "auto"  # auto, transparent, opaque
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            logger.info(f"🎨 Generating advertisement: {prompt[:100]}...")
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/images/generations",
                    json=payload,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"OpenAI error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"OpenAI error: {error_text}"}
                
                result = response.json()
                
                # Para gpt-image-1, la respuesta puede venir en diferentes formatos
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]

                    # Intentar obtener la imagen en diferentes formatos
                    b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                    image_url_direct = image_data.get("url")

                    if b64_image:
                        image_url = f"data:image/png;base64,{b64_image}"
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("revised_prompt"),
                            "metadata": {
                                "model": "gpt-image-1",
                                "size": size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt
                            }
                        }
                    elif image_url_direct:
                        return {
                            "success": True,
                            "image_url": image_url_direct,
                            "revised_prompt": image_data.get("revised_prompt"),
                            "metadata": {
                                "model": "gpt-image-1",
                                "size": size,
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt
                            }
                        }
                    else:
                        logger.error(f"No image data found in response: {image_data}")
                        return {"success": False, "error": "No image data in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}
                    
        except Exception as e:
            logger.error(f"Error generating advertisement: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def multi_turn_edit(self, previous_response_id: str, edit_prompt: str) -> Dict[str, Any]:
        """
        Edit an existing advertisement using multi-turn generation.
        
        Args:
            previous_response_id: ID of the previous response to build upon
            edit_prompt: Description of the changes to make
            
        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            payload = {
                "model": "gpt-4.1-mini",
                "previous_response_id": previous_response_id,
                "input": edit_prompt,
                "tools": [{"type": "image_generation"}]
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            logger.info(f"🔄 Multi-turn editing: {edit_prompt[:100]}...")
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/responses",
                    json=payload,
                    headers=headers
                )
                
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"OpenAI multi-turn error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"OpenAI error: {error_text}"}
                
                result = response.json()
                
                # Extract image from multi-turn response
                image_data = [
                    output.get("result")
                    for output in result.get("output", [])
                    if output.get("type") == "image_generation_call"
                ]
                
                if image_data:
                    image_base64 = image_data[0]
                    image_url = f"data:image/png;base64,{image_base64}"
                    return {
                        "success": True,
                        "image_url": image_url,
                        "response_id": result.get("id"),
                        "metadata": {
                            "model": "gpt-4.1-mini",
                            "edit_prompt": edit_prompt,
                            "type": "multi_turn_edit"
                        }
                    }
                else:
                    return {"success": False, "error": "No image data in multi-turn response"}
                    
        except Exception as e:
            logger.error(f"Error in multi-turn edit: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def stream_generation(self, prompt: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate advertisement with streaming partial images.
        
        Args:
            prompt: Description of the advertisement to create
            
        Yields:
            Dict with partial image data and progress information
        """
        try:
            # Mejorar el prompt para generación de anuncios
            enhanced_prompt = f"""Crea una imagen de anuncio profesional y de alta calidad con el siguiente concepto: {prompt}. Usa calidad de producción a nivel de agencia, product placement profesional y estándares de fotografía comercial."""
            
            payload = {
                "model": "gpt-4.1",
                "input": enhanced_prompt,
                "stream": True,
                "tools": [{"type": "image_generation", "partial_images": 2}]
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            logger.info(f"🌊 Streaming advertisement generation: {prompt[:100]}...")
            
            async with httpx.AsyncClient(timeout=180.0) as client:
                async with client.stream(
                    "POST",
                    f"{self.base_url}/responses",
                    json=payload,
                    headers=headers
                ) as response:
                    
                    if response.status_code != 200:
                        error_text = await response.aread()
                        logger.error(f"OpenAI streaming error {response.status_code}: {error_text}")
                        yield {"success": False, "error": f"OpenAI error: {error_text}"}
                        return
                    
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            try:
                                data = json.loads(line[6:])
                                
                                if data.get("type") == "response.image_generation_call.partial_image":
                                    idx = data.get("partial_image_index", 0)
                                    image_base64 = data.get("partial_image_b64")
                                    
                                    if image_base64:
                                        image_url = f"data:image/png;base64,{image_base64}"
                                        yield {
                                            "success": True,
                                            "partial_image": True,
                                            "image_url": image_url,
                                            "index": idx,
                                            "progress": (idx + 1) * 50  # Assuming 2 partial images
                                        }
                                        
                            except json.JSONDecodeError:
                                continue
                                
        except Exception as e:
            logger.error(f"Error in streaming generation: {e}")
            yield {"success": False, "error": f"Error: {str(e)}"}

    def encode_image(self, file_content: bytes) -> str:
        """
        Encode image content to base64.

        Args:
            file_content: The image content as bytes

        Returns:
            Base64 encoded string
        """
        return base64.b64encode(file_content).decode("utf-8")

    async def edit_with_references(self, prompt: str, reference_images: List[UploadFile], size: str = "auto") -> Dict[str, Any]:
        """
        Generate advertisement using reference images.
        First analyzes the reference images with GPT-4 Vision, then generates with gpt-image-1.

        Args:
            prompt: Description of the advertisement to create
            reference_images: List of reference images to use
            size: Image size

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            logger.info(f"🖼️ Analyzing {len(reference_images)} reference images...")

            # Paso 1: Analizar las imágenes de referencia con GPT-4 Vision
            analysis_content = [
                {
                    "type": "text",
                    "text": f"IMPORTANTE: Responde ÚNICAMENTE en español. Analiza estas imágenes de referencia y describe detalladamente el estilo visual, composición, iluminación, product placement y elementos de diseño que observas. Luego crea un prompt detallado EN ESPAÑOL para generar un anuncio profesional de calidad de agencia con el siguiente concepto: '{prompt}'. El prompt debe incorporar los elementos visuales y de estilo que identificaste en las imágenes de referencia, usando estándares de fotografía comercial profesional. Incluye especificaciones técnicas de iluminación, composición y product placement. Tu respuesta completa debe estar en español, incluyendo el prompt final que será usado para generar la imagen. NO traduzcas nada al inglés."
                }
            ]

            # Agregar imágenes al contenido
            for i, ref_image in enumerate(reference_images):
                image_content = await ref_image.read()
                base64_image = self.encode_image(image_content)

                analysis_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{base64_image}",
                        "detail": "high"
                    }
                })

            # Llamada a GPT-4 Vision para analizar las referencias
            vision_payload = {
                "model": "gpt-4o",  # Modelo con capacidades de visión
                "messages": [
                    {
                        "role": "system",
                        "content": "Eres un experto en publicidad y fotografía comercial. SIEMPRE respondes en español. Tu tarea es analizar imágenes de referencia y crear prompts detallados en español para generar anuncios profesionales de calidad de agencia. Nunca traduzcas al inglés."
                    },
                    {
                        "role": "user",
                        "content": analysis_content
                    }
                ],
                "max_tokens": 1000
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            async with httpx.AsyncClient(timeout=120.0) as client:
                # Analizar imágenes de referencia
                vision_response = await client.post(
                    f"{self.base_url}/chat/completions",
                    json=vision_payload,
                    headers=headers
                )

                if vision_response.status_code != 200:
                    error_text = vision_response.text
                    logger.error(f"OpenAI vision analysis error {vision_response.status_code}: {error_text}")
                    return {"success": False, "error": f"Error analyzing references: {error_text}"}

                vision_result = vision_response.json()
                enhanced_prompt = vision_result["choices"][0]["message"]["content"]

                logger.info(f"✨ Enhanced prompt created from references: {enhanced_prompt[:200]}...")

                # Paso 2: Generar anuncio con el prompt mejorado
                generation_payload = {
                    "model": "gpt-image-1",
                    "prompt": enhanced_prompt,
                    "n": 1,
                    "size": size,
                    "quality": "auto",
                    "output_format": "png",
                    "background": "auto"
                }

                generation_response = await client.post(
                    f"{self.base_url}/images/generations",
                    json=generation_payload,
                    headers=headers
                )

                if generation_response.status_code != 200:
                    error_text = generation_response.text
                    logger.error(f"OpenAI generation error {generation_response.status_code}: {error_text}")
                    return {"success": False, "error": f"Error generating image: {error_text}"}

                result = generation_response.json()

                # Procesar respuesta de generación
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]

                    # Intentar obtener la imagen en diferentes formatos
                    b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                    image_url_direct = image_data.get("url")

                    if b64_image:
                        image_url = f"data:image/png;base64,{b64_image}"
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": enhanced_prompt,
                            "metadata": {
                                "model": "gpt-image-1",
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "reference_count": len(reference_images),
                                "type": "reference_edit",
                                "size": size
                            }
                        }
                    elif image_url_direct:
                        return {
                            "success": True,
                            "image_url": image_url_direct,
                            "revised_prompt": enhanced_prompt,
                            "metadata": {
                                "model": "gpt-image-1",
                                "original_prompt": prompt,
                                "enhanced_prompt": enhanced_prompt,
                                "reference_count": len(reference_images),
                                "type": "reference_edit",
                                "size": size
                            }
                        }
                    else:
                        logger.error(f"No image data found in response: {image_data}")
                        return {"success": False, "error": "No image data in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error in reference edit: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def edit_with_mask(self, image: UploadFile, mask: UploadFile, prompt: str) -> Dict[str, Any]:
        """
        Edit advertisement using a mask to specify areas to change.

        Args:
            image: The original image to edit
            mask: The mask image (white areas will be edited)
            prompt: Description of what to put in the masked area

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            # Read image and mask content
            image_content = await image.read()
            mask_content = await mask.read()

            # Save to temporary files for OpenAI API
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as img_temp:
                img_temp.write(image_content)
                img_temp_path = img_temp.name

            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as mask_temp:
                mask_temp.write(mask_content)
                mask_temp_path = mask_temp.name

            try:
                headers = {
                    "Authorization": f"Bearer {self.api_key}"
                }

                files = {
                    "image": ("image.png", open(img_temp_path, "rb"), "image/png"),
                    "mask": ("mask.png", open(mask_temp_path, "rb"), "image/png"),
                    "prompt": (None, prompt),
                    "model": (None, "gpt-image-1"),
                    "n": (None, "1"),
                    "output_format": (None, "png")
                }

                logger.info(f"✏️ Editing with mask: {prompt[:100]}...")

                async with httpx.AsyncClient(timeout=120.0) as client:
                    response = await client.post(
                        f"{self.base_url}/images/edits",
                        headers=headers,
                        files=files
                    )

                    if response.status_code != 200:
                        error_text = response.text
                        logger.error(f"OpenAI mask edit error {response.status_code}: {error_text}")
                        return {"success": False, "error": f"OpenAI error: {error_text}"}

                    result = response.json()

                    if "data" in result and len(result["data"]) > 0:
                        image_data = result["data"][0]
                        b64_image = image_data.get("b64_json")

                        if b64_image:
                            image_url = f"data:image/png;base64,{b64_image}"
                            return {
                                "success": True,
                                "image_url": image_url,
                                "metadata": {
                                    "model": "gpt-image-1",
                                    "prompt": prompt,
                                    "type": "mask_edit"
                                }
                            }
                        else:
                            return {"success": False, "error": "No base64 image data in mask edit response"}
                    else:
                        return {"success": False, "error": "No image data in mask edit response"}

            finally:
                # Clean up temporary files
                try:
                    os.unlink(img_temp_path)
                    os.unlink(mask_temp_path)
                except:
                    pass

        except Exception as e:
            logger.error(f"Error in mask edit: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}


# Global service instance
ad_service = AdService()
