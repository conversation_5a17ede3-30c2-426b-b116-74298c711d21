-- Tabla para almacenar los núcleos de marca
CREATE TABLE nucleos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  
  -- Información básica
  brand_name TEXT NOT NULL,
  website TEXT,
  industry TEXT NOT NULL,
  
  -- Identidad visual
  logo_url TEXT,
  primary_color TEXT NOT NULL DEFAULT '#3018ef',
  secondary_color TEXT NOT NULL DEFAULT '#dd3a5a',
  
  -- Audiencia y tono
  target_audience TEXT NOT NULL,
  tone TEXT NOT NULL,
  personality TEXT[] DEFAULT '{}',
  
  -- Posicionamiento
  description TEXT NOT NULL,
  unique_value TEXT NOT NULL,
  competitors TEXT,
  
  -- Documentos y ejemplos
  documents TEXT[] DEFAULT '{}',
  examples TEXT,
  
  -- Metadata
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'archived')),
  campaigns_count INTEGER DEFAULT 0,
  assets_count INTEGER DEFAULT 0,
  
  -- Usuario (opcional para multi-tenant)
  user_id TEXT
);

-- Índices para mejorar performance
CREATE INDEX idx_nucleos_user_id ON nucleos(user_id);
CREATE INDEX idx_nucleos_status ON nucleos(status);
CREATE INDEX idx_nucleos_updated_at ON nucleos(updated_at DESC);
CREATE INDEX idx_nucleos_brand_name ON nucleos(brand_name);

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para actualizar updated_at
CREATE TRIGGER update_nucleos_updated_at 
    BEFORE UPDATE ON nucleos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Habilitar Row Level Security (RLS)
ALTER TABLE nucleos ENABLE ROW LEVEL SECURITY;

-- Política para que los usuarios solo vean sus propios núcleos
CREATE POLICY "Users can view their own nucleos" ON nucleos
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own nucleos" ON nucleos
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own nucleos" ON nucleos
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own nucleos" ON nucleos
    FOR DELETE USING (auth.uid()::text = user_id);

-- Insertar algunos datos de ejemplo (opcional)
INSERT INTO nucleos (
  brand_name, 
  industry, 
  target_audience, 
  tone, 
  description, 
  unique_value,
  personality,
  status
) VALUES 
(
  'Emma Studio',
  'SaaS & Tecnología',
  'Marketers, agencias de marketing, emprendedores que buscan automatizar y mejorar sus estrategias de marketing con IA',
  'Innovador y Visionario',
  'Plataforma de marketing con IA revolucionaria que permite a las marcas ejecutar campañas inteligentes sin empezar desde cero',
  'Emma aprende la identidad de tu marca una sola vez y ejecuta con ese contexto en todas las herramientas, eliminando la necesidad de repetir briefs',
  ARRAY['Innovadora', 'Confiable', 'Moderna', 'Experta'],
  'active'
),
(
  'TechFlow Solutions',
  'Tecnología',
  'CTOs, desarrolladores, empresas tecnológicas que buscan soluciones escalables',
  'Técnico y Preciso',
  'Soluciones tecnológicas avanzadas para empresas que buscan optimizar sus procesos digitales',
  'Combinamos expertise técnico con innovación para crear soluciones que realmente escalan',
  ARRAY['Técnica', 'Confiable', 'Innovadora', 'Experta'],
  'active'
);
