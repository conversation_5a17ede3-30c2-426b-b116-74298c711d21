# 🚀 Configuración de Vibe Marketing

## ⚠️ IMPORTANTE: API Keys Requeridas

Vibe Marketing **NO tiene fallbacks ni datos simulados**. Para que funcione correctamente, debes configurar las siguientes API keys:

## 🔑 API Keys Necesarias

### 1. Google Gemini API Key
**Propósito**: Análisis de contenido con IA, generación de estrategias SEO/LLM

**Cómo obtenerla**:
1. Ve a [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Crea una nueva API key
3. Copia la key y agrégala a tu `.env`

```bash
GEMINI_API_KEY=tu_gemini_api_key_aqui
```

### 2. Serper API Key
**Propósito**: Análisis SERP (resultados de búsqueda de Google)

**Cómo obtenerla**:
1. Ve a [Serper.dev](https://serper.dev/)
2. Regístrate y obtén tu API key gratuita
3. Copia la key y agrégala a tu `.env`

```bash
SERPER_API_KEY=tu_serper_api_key_aqui
```

## 📋 Configuración Paso a Paso

### 1. Configurar Variables de Entorno

Crea o actualiza tu archivo `.env` en la carpeta `backend/`:

```bash
# Copia el archivo de ejemplo
cp backend/.env.example backend/.env

# Edita el archivo .env y agrega tus API keys
nano backend/.env
```

### 2. Verificar Configuración

Asegúrate de que tu archivo `.env` contenga:

```bash
# REQUERIDAS para Vibe Marketing
GEMINI_API_KEY=tu_gemini_api_key_real
SERPER_API_KEY=tu_serper_api_key_real

# Otras APIs (opcionales para otras funciones)
OPENAI_API_KEY=tu_openai_key
STABILITY_API_KEY=tu_stability_key
# ... etc
```

### 3. Reiniciar el Backend

Después de configurar las API keys:

```bash
cd backend
poetry run uvicorn app.main:app --reload
```

### 4. Probar la Funcionalidad

1. Ve a `http://localhost:5173/vibe-marketing`
2. Haz clic en "SEO + LLM Optimizer"
3. Ingresa una keyword (ej: "cómo hacer ejercicio en casa")
4. Haz clic en "Analizar"

## ❌ Qué Pasa Sin API Keys

Si no configuras las API keys, verás errores como:

- **Sin Gemini API Key**: "GEMINI_API_KEY not configured. Please set your Gemini API key."
- **Sin Serper API Key**: "SERPER_API_KEY not configured. Please set your Serper API key."

## 🔍 Troubleshooting

### Error: "GEMINI_API_KEY not configured"
- ✅ Verifica que la variable esté en tu `.env`
- ✅ Reinicia el servidor backend
- ✅ Verifica que la API key sea válida en Google AI Studio

### Error: "SERPER_API_KEY not configured"
- ✅ Verifica que la variable esté en tu `.env`
- ✅ Reinicia el servidor backend
- ✅ Verifica que tengas créditos en tu cuenta Serper

### Error: "Content strategy analysis failed"
- ✅ Verifica tu conexión a internet
- ✅ Verifica que la API key de Gemini tenga permisos
- ✅ Revisa los logs del backend para más detalles

### Error: "SERP analysis failed"
- ✅ Verifica que la API key de Serper sea válida
- ✅ Verifica que tengas créditos disponibles
- ✅ Intenta con una keyword diferente

## 💰 Costos de las APIs

### Google Gemini
- **Gratis**: 15 requests por minuto
- **Pagado**: $0.00025 por 1K caracteres de input
- **Recomendación**: Empieza con el plan gratuito

### Serper.dev
- **Gratis**: 2,500 búsquedas gratis
- **Pagado**: $50 por 100K búsquedas
- **Recomendación**: El plan gratuito es suficiente para empezar

## 🎯 Funcionalidades Disponibles

Una vez configurado correctamente, tendrás acceso a:

### ✅ SEO + LLM Optimizer
- Análisis SERP real de Google
- Estrategia de contenido generada por Gemini
- Scores SEO y LLM calculados
- Estructura sugerida H1/H2/H3
- FAQs relevantes
- Entidades importantes
- Tips de optimización

### 🔜 Próximamente
- Viral Content Creator
- Campaign Orchestrator
- Editor en tiempo real

## 📞 Soporte

Si tienes problemas con la configuración:

1. **Revisa los logs**: `docker logs emma-backend` o revisa la consola del servidor
2. **Verifica las API keys**: Prueba las keys directamente en las plataformas
3. **Reinicia todo**: Backend y frontend
4. **Revisa la documentación**: `VIBE_MARKETING_V2.md`

## 🚀 ¡Listo para Usar!

Una vez configurado, Vibe Marketing te dará análisis reales y estrategias de contenido potentes para dominar tanto Google como los LLMs. ¡Es la primera herramienta del mercado que hace esto! 🎉
